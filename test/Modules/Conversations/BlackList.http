### Variables
@baseUrl = https://localhost:5001/api/v1
@token = {{$dotenv TOKEN}}

### Create BlackList Entry
POST {{baseUrl}}/conversations/blacklist
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "contactType": 2,
  "contactValue": "+905551234567",
  "blockedChannels": 1,
  "customerId": null,
  "reason": "Spam arama",
  "expiryDate": "2024-12-31T23:59:59Z"
}

### Create BlackList Entry - Email
POST {{baseUrl}}/conversations/blacklist
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "contactType": 1,
  "contactValue": "<EMAIL>",
  "blockedChannels": 4,
  "customerId": null,
  "reason": "Spam email",
  "expiryDate": null
}

### Get BlackList Entry
GET {{baseUrl}}/conversations/blacklist/{{blackListId}}
Authorization: Bearer {{token}}

### List BlackList Entries
GET {{baseUrl}}/conversations/blacklist?pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

### List BlackList Entries with Search
GET {{baseUrl}}/conversations/blacklist?pageNumber=1&pageSize=10&searchTerm=spam
Authorization: Bearer {{token}}

### List BlackList Entries with Filters
GET {{baseUrl}}/conversations/blacklist?pageNumber=1&pageSize=10&contactType=2&isActive=true&blockedChannels=1
Authorization: Bearer {{token}}

### List Expired BlackList Entries
GET {{baseUrl}}/conversations/blacklist?pageNumber=1&pageSize=10&isExpired=true
Authorization: Bearer {{token}}

### Update BlackList Entry
PUT {{baseUrl}}/conversations/blacklist/{{blackListId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": "{{blackListId}}",
  "contactType": 2,
  "contactValue": "+905551234567",
  "blockedChannels": 7,
  "customerId": null,
  "reason": "Güncellenmiş spam arama",
  "expiryDate": "2025-12-31T23:59:59Z",
  "isActive": true
}

### Check BlackList - Phone Call
POST {{baseUrl}}/conversations/blacklist/check
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "contactValue": "+905551234567",
  "contactType": 2,
  "channel": 1
}

### Check BlackList - Email
POST {{baseUrl}}/conversations/blacklist/check
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "contactValue": "<EMAIL>",
  "contactType": 1,
  "channel": 4
}

### Check BlackList - SMS
POST {{baseUrl}}/conversations/blacklist/check
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "contactValue": "+905551234567",
  "contactType": 2,
  "channel": 2
}

### Delete BlackList Entry
DELETE {{baseUrl}}/conversations/blacklist/{{blackListId}}
Authorization: Bearer {{token}}

### Test Data Setup - Create Multiple Entries
POST {{baseUrl}}/conversations/blacklist
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "contactType": 2,
  "contactValue": "+905559876543",
  "blockedChannels": 3,
  "customerId": null,
  "reason": "Test verisi - Arama ve SMS engelli",
  "expiryDate": null
}

###
POST {{baseUrl}}/conversations/blacklist
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "contactType": 1,
  "contactValue": "<EMAIL>",
  "blockedChannels": 4,
  "customerId": null,
  "reason": "Test verisi - Email engelli",
  "expiryDate": "2024-06-30T23:59:59Z"
}

###
POST {{baseUrl}}/conversations/blacklist
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "contactType": 3,
  "contactValue": "<EMAIL>",
  "blockedChannels": 7,
  "customerId": null,
  "reason": "Test verisi - Tüm kanallar engelli",
  "expiryDate": null
}
