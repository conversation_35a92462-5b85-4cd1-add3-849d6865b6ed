# Functional Requirements Document - BlackList <PERSON>yapısı

## <PERSON><PERSON> Ba<PERSON>ış
<PERSON>, MasterCRM uygulamasının Conversations modülü içerisinde yer alacak BlackList altyapısının teknik gereksinimlerini ve implementasyon detaylarını içermektedir. BlackList <PERSON><PERSON><PERSON>, hem arama hem de mesajlaşma işlemlerinde kullanılarak istenmeyen iletişimleri engellemek amacıyla geliştirilecektir.

## Motivasyon
- Spam aramaları ve mesajları engelleme
- İstenmeyen müşteri iletişimlerini filtreleme
- Sistemde kayıtlı olmayan kişileri de engelleme imkanı
- Esnek engelleme kuralları oluşturma
- Geçici ve kalıcı engelleme seçenekleri

## Teknik Gereksinimler

### Database Schema

```sql
-- Conversations Module içerisinde
CREATE TABLE Conversations.BlackList (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),

    -- BlackList specific fields
    ContactType NVARCHAR(20) NOT NULL, -- 'Email', 'Phone', 'Both'
    ContactValue NVARCHAR(255) NOT NULL, -- Email adresi veya telefon numarası
    CustomerId UNIQUEIDENTIFIER NULL, -- Müşteri referansı (varsa)
    Reason NVARCHAR(500), -- Engelleme nedeni
    IsActive BIT NOT NULL DEFAULT 1, -- Aktif/Pasif durumu
    ExpiryDate DATETIME2 NULL, -- Geçici engelleme için son tarih
    BlockedChannels NVARCHAR(100) NOT NULL, -- 'Call', 'SMS', 'Email', 'All'

    -- Indexes
    INDEX IX_BlackList_ContactValue (ContactValue),
    INDEX IX_BlackList_CustomerId (CustomerId),
    INDEX IX_BlackList_IsActive_ContactType (IsActive, ContactType),
    INDEX IX_BlackList_IsActive_ExpiryDate (IsActive, ExpiryDate)
);
```

### Klasör Yapısı

```
Modules/
├── Conversations/
│   ├── Application/
│   │   ├── BlackList/
│   │   │   ├── CreateBlackList/
│   │   │   │   ├── CreateBlackListCommand.cs
│   │   │   │   ├── CreateBlackListCommandHandler.cs
│   │   │   │   ├── CreateBlackListCommandValidator.cs
│   │   │   │   └── CreateBlackListEndpoint.cs
│   │   │   ├── GetBlackList/
│   │   │   │   ├── GetBlackListQuery.cs
│   │   │   │   ├── GetBlackListQueryHandler.cs
│   │   │   │   └── GetBlackListEndpoint.cs
│   │   │   ├── CheckBlackList/
│   │   │   │   ├── CheckBlackListQuery.cs
│   │   │   │   ├── CheckBlackListQueryHandler.cs
│   │   │   │   └── CheckBlackListEndpoint.cs
│   │   │   ├── UpdateBlackList/
│   │   │   ├── DeleteBlackList/
│   │   │   └── ListBlackLists/
│   │   ├── Chat/
│   │   ├── Calls/
│   │   └── ...
│   └── Infrastructure/
│       ├── Data/
│       │   ├── IConversationDbContext.cs
│       │   └── ConversationDbContext.cs
│       └── ...
```

### Domain Models

```csharp
// Conversations/Application/BlackList/Models/BlackList.cs
namespace MasterCRM.Modules.Conversations.Application.BlackList.Models;

public class BlackList
{
    public Guid Id { get; set; }
    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public Guid? InsertUserId { get; set; }
    public Guid? UpdateUserId { get; set; }
    public string? History { get; set; }

    public ContactType ContactType { get; set; }
    public string ContactValue { get; set; } = string.Empty;
    public Guid? CustomerId { get; set; }
    public string? Reason { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime? ExpiryDate { get; set; }
    public BlockedChannels BlockedChannels { get; set; }
}

public enum ContactType
{
    Email = 1,
    Phone = 2,
    Both = 3
}

public enum BlockedChannels
{
    Call = 1,
    SMS = 2,
    Email = 4,
    All = 7 // Call | SMS | Email
}
```

### Database Context Interface

```csharp
// Infrastructure/Data/IConversationDbContext.cs
namespace MasterCRM.Modules.Conversations.Infrastructure.Data;

public interface IConversationDbContext
{
    DbSet<BlackList> BlackLists { get; set; }
    // Diğer DbSet'ler...

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
```

## Command/Query Örnekleri

### Create BlackList Entry

```csharp
// CreateBlackListCommand.cs
namespace MasterCRM.Modules.Conversations.Application.BlackList.CreateBlackList;

public record CreateBlackListCommand(
    ContactType ContactType,
    string ContactValue,
    Guid? CustomerId,
    string? Reason,
    DateTime? ExpiryDate,
    BlockedChannels BlockedChannels
) : IRequest<Result<Guid>>;

// CreateBlackListCommandHandler.cs
public class CreateBlackListCommandHandler(IConversationDbContext context)
    : IRequestHandler<CreateBlackListCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(
        CreateBlackListCommand request,
        CancellationToken cancellationToken)
    {
        var blackList = new BlackList
        {
            Id = Guid.NewGuid(),
            InsertDate = DateTime.UtcNow,
            ContactType = request.ContactType,
            ContactValue = request.ContactValue.Trim().ToLowerInvariant(),
            CustomerId = request.CustomerId,
            Reason = request.Reason,
            ExpiryDate = request.ExpiryDate,
            BlockedChannels = request.BlockedChannels,
            IsActive = true
        };

        context.BlackLists.Add(blackList);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(blackList.Id);
    }
}

// CreateBlackListCommandValidator.cs
public class CreateBlackListCommandValidator : AbstractValidator<CreateBlackListCommand>
{
    public CreateBlackListCommandValidator()
    {
        RuleFor(x => x.ContactValue)
            .NotEmpty()
            .WithMessage("İletişim bilgisi boş olamaz");

        RuleFor(x => x.ContactValue)
            .Must(BeValidEmailOrPhone)
            .WithMessage("Geçerli bir email adresi veya telefon numarası giriniz");

        RuleFor(x => x.ExpiryDate)
            .GreaterThan(DateTime.UtcNow)
            .When(x => x.ExpiryDate.HasValue)
            .WithMessage("Son tarih gelecek bir tarih olmalıdır");
    }

    private bool BeValidEmailOrPhone(string contactValue)
    {
        // Email veya telefon numarası format kontrolü
        return IsValidEmail(contactValue) || IsValidPhone(contactValue);
    }

    private bool IsValidEmail(string email)
    {
        return email.Contains("@") && email.Contains(".");
    }

    private bool IsValidPhone(string phone)
    {
        return phone.All(char.IsDigit) && phone.Length >= 10;
    }
}

// CreateBlackListEndpoint.cs
public class CreateBlackListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/blacklist", async (
                CreateBlackListCommand command,
                ISender sender) =>
            {
                var result = await sender.Send(command);
                return result.IsSuccess ? Results.Ok(result) : Results.BadRequest(result);
            })
            .WithTags("Conversations.BlackList")
            .WithGroupName("apiv1")
            .Produces<Result<Guid>>(200)
            .Produces<Result>(400)
            .RequireAuthorization();
    }
}
```

### Get BlackList Entry

```csharp
// GetBlackListQuery.cs
namespace MasterCRM.Modules.Conversations.Application.BlackList.GetBlackList;

public record GetBlackListQuery(Guid Id) : IRequest<Result<BlackListDto>>;

public record BlackListDto(
    Guid Id,
    ContactType ContactType,
    string ContactValue,
    Guid? CustomerId,
    string? Reason,
    bool IsActive,
    DateTime? ExpiryDate,
    BlockedChannels BlockedChannels,
    DateTime InsertDate
);

// GetBlackListQueryHandler.cs
public class GetBlackListQueryHandler(IConversationDbContext context)
    : IRequestHandler<GetBlackListQuery, Result<BlackListDto>>
{
    public async Task<Result<BlackListDto>> Handle(
        GetBlackListQuery request,
        CancellationToken cancellationToken)
    {
        var blackList = await context.BlackLists
            .Where(x => x.Id == request.Id)
            .Select(x => new BlackListDto(
                x.Id,
                x.ContactType,
                x.ContactValue,
                x.CustomerId,
                x.Reason,
                x.IsActive,
                x.ExpiryDate,
                x.BlockedChannels,
                x.InsertDate))
            .FirstOrDefaultAsync(cancellationToken);

        if (blackList == null)
        {
            return Result.Failure<BlackListDto>("BlackList kaydı bulunamadı");
        }

        return Result.Success(blackList);
    }
}

// GetBlackListEndpoint.cs
public class GetBlackListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/conversations/blacklist/{id}", async (
                Guid id,
                ISender sender) =>
            {
                var result = await sender.Send(new GetBlackListQuery(id));
                return result.IsSuccess ? Results.Ok(result) : Results.NotFound(result);
            })
            .WithTags("Conversations.BlackList")
            .WithGroupName("apiv1")
            .Produces<Result<BlackListDto>>(200)
            .Produces<Result>(404)
            .RequireAuthorization();
    }
}
```

## API Endpoint Örneği

### Create BlackList Entry Endpoint

**Request:**
```http
POST /api/v1/conversations/blacklist
Content-Type: application/json

{
    "contactType": 2,
    "contactValue": "+************",
    "customerId": null,
    "reason": "Spam arama",
    "expiryDate": "2024-12-31T23:59:59Z",
    "blockedChannels": 1
}
```

**Response (Success):**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
    "isSuccess": true,
    "value": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "error": null
}
```

**Response (Validation Error):**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
    "isSuccess": false,
    "value": null,
    "error": "İletişim bilgisi boş olamaz"
}
```

## Cache Stratejisi

BlackList kontrolleri için cache stratejisi:

```csharp
// CheckBlackListQueryHandler.cs içerisinde
var cacheKey = $"blacklist:{request.ContactType}:{request.ContactValue.ToLowerInvariant()}";
var result = await _hybridCache.GetOrSetAsync(
    cacheKey,
    async () => await CheckBlackListFromDatabase(request.ContactValue, request.ContactType),
    TimeSpan.FromMinutes(15)
);
```

## Kullanım Senaryoları

1. **Arama Engelleme**: 3CX entegrasyonunda gelen arama numarası BlackList'te kontrol edilir
2. **Mesaj Engelleme**: Chat veya email gönderimi öncesinde BlackList kontrolü yapılır
3. **Müşteri Kaydı**: Yeni müşteri kaydı sırasında BlackList kontrolü
4. **Toplu İşlemler**: Batch işlemlerinde BlackList filtrelemesi

## Başarı Kriterleri

- BlackList kontrolü < 50ms response time
- Cache hit ratio > %80
- Günlük 10,000+ kontrol işlemi desteklemeli
- %99.9 availability

## Güvenlik Gereksinimleri

- Tüm BlackList işlemleri audit log'a kaydedilmeli
- Sadece yetkili kullanıcılar BlackList yönetebilmeli
- Contact value'lar hash'lenerek saklanabilir (opsiyonel)

Bu doküman, BlackList altyapısının Conversations modülü içerisinde implementasyonu için gerekli tüm teknik detayları içermektedir.