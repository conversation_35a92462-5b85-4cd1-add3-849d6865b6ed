using System.Linq.Expressions;
using System.Reflection;

namespace Shared.Application.Extensions;

public static class QueryFilterExtensions
{
    public static IQueryable<T> ApplyFilters<T>(this IQueryable<T> query, object request) where T : class
    {
        var requestType = request.GetType();
        var entityType = typeof(T);
        var filters = new List<(bool condition, Func<IQueryable<T>, IQueryable<T>> filter)>();

        // Get all properties from request
        var properties = requestType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            var value = property.GetValue(request);
            if (value == null)
            {
                continue;
            }
            // Skip pagination and sorting properties
            if (IsPaginationProperty(property.Name))
            {
                continue;
            }
            var filter = CreateFilter<T>(property, value, entityType);
            if (filter.hasCondition)
            {
                filters.Add((filter.hasCondition, filter.filterFunc));
            }
        }
        // Apply all filters
        return filters.Aggregate(query, (current, filter) =>
            filter.condition ? filter.filter(current) : current);
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateFilter<T>(
        PropertyInfo property, object value, Type entityType) where T : class
    {
        // String filters
        if (property.PropertyType == typeof(string) && value is string stringValue && !string.IsNullOrWhiteSpace(stringValue))
        {
            return CreateStringFilter<T>(property.Name, stringValue.Trim().ToLower(), entityType);
        }

        // Nullable value type filters
        if (IsNullableValueType(property.PropertyType) && value != null)
        {
            return CreateValueFilter<T>(property.Name, value, entityType);
        }
        // Array filters
        if (property.PropertyType.IsArray && value is Array arrayValue && arrayValue.Length > 0)
        {
            return CreateArrayFilter<T>(property.Name, arrayValue, entityType);
        }
        // List filters
        if (IsGenericList(property.PropertyType) && value is IEnumerable<T> enumerableValue && enumerableValue.Cast<object>().Any())
        {
            return CreateListFilter<T>(property.Name, enumerableValue, entityType);
        }
        return (false, null);
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateStringFilter<T>(
        string propertyName, string value, Type entityType) where T : class
    {
        var entityProperty = FindEntityProperty(entityType, propertyName);
        if (entityProperty == null)
        {
            return (false, null);
        }
        return (true, query =>
        {
            var parameter = Expression.Parameter(entityType, "x");
            var property = Expression.Property(parameter, entityProperty);
            var toLower = Expression.Call(property, "ToLower", null);
            var constant = Expression.Constant(value);
            var contains = Expression.Call(toLower, "Contains", null, constant);
            var lambda = Expression.Lambda<Func<T, bool>>(contains, parameter);
            return query.Where(lambda);
        }
        );
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateValueFilter<T>(
        string propertyName, object value, Type entityType) where T : class
    {
        var entityProperty = FindEntityProperty(entityType, propertyName);
        if (entityProperty == null)
        {
            return (false, null);
        }
        return (true, query =>
        {
            var parameter = Expression.Parameter(entityType, "x");
            var property = Expression.Property(parameter, entityProperty);
            var constant = Expression.Constant(value);
            var equal = Expression.Equal(property, constant);
            var lambda = Expression.Lambda<Func<T, bool>>(equal, parameter);
            return query.Where(lambda);
        }
        );
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateArrayFilter<T>(
        string propertyName, Array arrayValue, Type entityType) where T : class
    {
        var entityProperty = FindEntityProperty(entityType, propertyName);
        if (entityProperty == null)
        {
            return (false, null);
        }
        return (true, query =>
        {
            var parameter = Expression.Parameter(entityType, "x");
            var property = Expression.Property(parameter, entityProperty);
            var elementType = arrayValue.GetType().GetElementType();

            // Convert array to IEnumerable<T> for Contains method
            var arrayAsEnumerable = Expression.Convert(Expression.Constant(arrayValue), typeof(IEnumerable<>).MakeGenericType(elementType));

            // Enumerable.Contains(IEnumerable<T> source, T value)
            var containsMethod = typeof(Enumerable).GetMethods()
                .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                .MakeGenericMethod(elementType);

            var contains = Expression.Call(containsMethod, arrayAsEnumerable, property);
            var lambda = Expression.Lambda<Func<T, bool>>(contains, parameter);
            return query.Where(lambda);
        }
        );
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateListFilter<T>(
        string propertyName, IEnumerable<T> enumerableValue, Type entityType) where T : class
    {
        var entityProperty = FindEntityProperty(entityType, propertyName);
        if (entityProperty == null)
        {
            return (false, null);
        }

        return (true, query =>
        {
            var parameter = Expression.Parameter(entityType, "x");
            var property = Expression.Property(parameter, entityProperty);
            var elementType = enumerableValue.GetType().GetGenericArguments()[0];

            // Convert to IEnumerable<T>
            var enumerableAsGeneric = Expression.Convert(Expression.Constant(enumerableValue), typeof(IEnumerable<>).MakeGenericType(elementType));

            // Enumerable.Contains(IEnumerable<T> source, T value)
            var containsMethod = typeof(Enumerable).GetMethods()
                .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                .MakeGenericMethod(elementType);

            var contains = Expression.Call(containsMethod, enumerableAsGeneric, property);
            var lambda = Expression.Lambda<Func<T, bool>>(contains, parameter);
            return query.Where(lambda);
        }
        );
    }

    private static PropertyInfo FindEntityProperty(Type entityType, string propertyName)
    {
        // Direct property match
        var property = entityType.GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance);
        if (property != null)
        {
            return property;
        }

        // Try removing common suffixes like "Id", "Ids"
        var cleanName = propertyName.Replace("Ids", "").Replace("Id", "");
        property = entityType.GetProperty(cleanName, BindingFlags.Public | BindingFlags.Instance);
        if (property != null)
        {
            return property;
        }

        // Case insensitive search
        property = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .FirstOrDefault(p => string.Equals(p.Name, propertyName, StringComparison.OrdinalIgnoreCase));

        return property;
    }

    private static bool IsNullableValueType(Type type)
    {
        return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>);
    }

    private static bool IsGenericList(Type type)
    {
        return type.IsGenericType &&
                (type.GetGenericTypeDefinition() == typeof(List<>) ||
                type.GetGenericTypeDefinition() == typeof(IList<>) ||
                type.GetGenericTypeDefinition() == typeof(ICollection<>));
    }

    private static bool IsPaginationProperty(string propertyName)
    {
        var paginationProperties = new[] { "PageNumber", "PageSize", "SortProperty", "SortType", "SortDirection" };
        return paginationProperties.Contains(propertyName);
    }
}

public static class AdvancedQueryFilterExtensions
{
    public static IQueryable<T> ApplyAdvancedFilters<T>(this IQueryable<T> query, object request) where T : class
    {
        var requestType = request.GetType();
        var entityType = typeof(T);
        var filters = new List<(bool condition, Func<IQueryable<T>, IQueryable<T>> filter)>();

        var properties = requestType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            var value = property.GetValue(request);
            if (value == null)
            {
                continue;
            }
            if (IsPaginationProperty(property.Name))
            {
                continue;
            }
            var filterAttribute = property.GetCustomAttribute<FilterPropertyAttribute>();
            var filter = CreateAdvancedFilter<T>(property, value, entityType, filterAttribute);

            if (filter.hasCondition)
            {
                filters.Add((filter.hasCondition, filter.filterFunc));
            }
        }

        return filters.Aggregate(query, (current, filter) =>
            filter.condition ? filter.filter(current) : current);
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateAdvancedFilter<T>(
        PropertyInfo property, object value, Type entityType, FilterPropertyAttribute attribute) where T : class
    {
        var entityPropertyName = !string.IsNullOrWhiteSpace(attribute?.EntityPropertyName)
            ? attribute.EntityPropertyName
            : property.Name;
        var filterType = attribute?.FilterType ?? FilterType.Equal;
        var ignoreCase = attribute?.IgnoreCase ?? true;

        var entityProperty = FindEntityProperty(entityType, entityPropertyName);
        if (entityProperty == null)
        {
            return (false, null);
        }
        // String filters
        if (property.PropertyType == typeof(string) && value is string stringValue && !string.IsNullOrWhiteSpace(stringValue))
        {
            return CreateStringFilterByType<T>(entityProperty, stringValue.Trim(), filterType, ignoreCase);
        }

        // Numeric and date filters
        if (IsNullableValueType(property.PropertyType) || entityProperty.PropertyType.IsValueType)
        {
            return CreateValueFilterByType<T>(entityProperty, value, filterType);
        }

        // Collection filters
        if (property.PropertyType.IsArray || IsGenericList(property.PropertyType))
        {
            return CreateCollectionFilter<T>(entityProperty, value);
        }

        return (false, null);
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateStringFilterByType<T>(
        PropertyInfo entityProperty, string value, FilterType filterType, bool ignoreCase) where T : class
    {
        return (true, query =>
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var property = Expression.Property(parameter, entityProperty);
            var stringValue = ignoreCase ? value.ToLower() : value;
            Expression propertyValue = ignoreCase ? Expression.Call(property, "ToLower", null) : property;
            var constant = Expression.Constant(stringValue);

            Expression condition = filterType switch
            {
                FilterType.Contains => Expression.Call(propertyValue, "Contains", null, constant),
                FilterType.StartsWith => Expression.Call(propertyValue, "StartsWith", null, constant),
                FilterType.EndsWith => Expression.Call(propertyValue, "EndsWith", null, constant),
                FilterType.Equal => Expression.Equal(propertyValue, constant),
                _ => Expression.Call(propertyValue, "Contains", null, constant)
            };

            var lambda = Expression.Lambda<Func<T, bool>>(condition, parameter);
            return query.Where(lambda);
        }
        );
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateValueFilterByType<T>(
    PropertyInfo entityProperty, object value, FilterType filterType) where T : class
    {
        return (true, query =>
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var property = Expression.Property(parameter, entityProperty);

            // Property tipini ve nullability durumunu kontrol et
            var propertyType = property.Type;
            var underlyingType = Nullable.GetUnderlyingType(propertyType);
            var isNullable = underlyingType != null || !propertyType.IsValueType;

            Expression condition;

            // Null değer kontrolü
            if (value == null)
            {
                condition = filterType switch
                {
                    FilterType.Equal => isNullable
                        ? Expression.Equal(property, Expression.Constant(null, propertyType))
                        : Expression.Constant(false),
                    _ => Expression.Constant(false) // Null için diğer operatörler geçersiz
                };
            }
            else
            {
                // Value'nun tipini property tipine uygun hale getir
                object convertedValue = value;
                if (underlyingType != null && value.GetType() == underlyingType)
                {
                    // Nullable<T> için uygun tip dönüşümü
                    convertedValue = Convert.ChangeType(value, underlyingType);
                }

                var constant = Expression.Constant(convertedValue, propertyType);

                condition = filterType switch
                {
                    FilterType.Equal => Expression.Equal(property, constant),
                    FilterType.GreaterThan => Expression.GreaterThan(property, constant),
                    FilterType.LessThan => Expression.LessThan(property, constant),
                    FilterType.GreaterThanOrEqual => Expression.GreaterThanOrEqual(property, constant),
                    FilterType.LessThanOrEqual => Expression.LessThanOrEqual(property, constant),
                    _ => Expression.Equal(property, constant)
                };
            }

            var lambda = Expression.Lambda<Func<T, bool>>(condition, parameter);
            return query.Where(lambda);
        }
        );
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateValueFilterByType2<T>(
        PropertyInfo entityProperty, object value, FilterType filterType) where T : class
    {
        return (true, query =>
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var property = Expression.Property(parameter, entityProperty);
            var constant = Expression.Constant(value);

            Expression condition = filterType switch
            {
                FilterType.Equal => Expression.Equal(property, constant),
                FilterType.GreaterThan => Expression.GreaterThan(property, constant),
                FilterType.LessThan => Expression.LessThan(property, constant),
                FilterType.GreaterThanOrEqual => Expression.GreaterThanOrEqual(property, constant),
                FilterType.LessThanOrEqual => Expression.LessThanOrEqual(property, constant),
                _ => Expression.Equal(property, constant)
            };

            var lambda = Expression.Lambda<Func<T, bool>>(condition, parameter);
            return query.Where(lambda);
        }
        );
    }

    private static (bool hasCondition, Func<IQueryable<T>, IQueryable<T>> filterFunc) CreateCollectionFilter<T>(
        PropertyInfo entityProperty, object value) where T : class
    {
        return (true, query =>
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var property = Expression.Property(parameter, entityProperty);

            var elementType = value.GetType().IsArray
                ? value.GetType().GetElementType()
                : value.GetType().GetGenericArguments()[0];

            // Convert to IEnumerable<T>
            var collectionAsEnumerable = Expression.Convert(Expression.Constant(value), typeof(IEnumerable<>).MakeGenericType(elementType));

            // Get the correct Contains method: Enumerable.Contains(IEnumerable<T> source, T value)
            var containsMethod = typeof(Enumerable).GetMethods()
                .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                .MakeGenericMethod(elementType);

            var contains = Expression.Call(containsMethod, collectionAsEnumerable, property);
            var lambda = Expression.Lambda<Func<T, bool>>(contains, parameter);
            return query.Where(lambda);
        }
        );
    }

    private static PropertyInfo FindEntityProperty(Type entityType, string propertyName)
    {
        if (string.IsNullOrWhiteSpace(propertyName))
        {
            return null;
        }
        var property = entityType.GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance);
        if (property != null)
        {
            return property;
        }
        property = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .FirstOrDefault(p => string.Equals(p.Name, propertyName, StringComparison.OrdinalIgnoreCase));
        if (property != null)
        {
            return property;
        }

        // if (propertyName.EndsWith("Ids") || propertyName.EndsWith("Id"))
        // {
        //     var cleanName = propertyName.Replace("Ids", "").Replace("Id", "");
        //     property = entityType.GetProperty(cleanName, BindingFlags.Public | BindingFlags.Instance);
        //     if (property != null)
        //     {
        //         return property;
        //     }

        //     // Case insensitive for cleaned name
        //     property = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
        //         .FirstOrDefault(p => string.Equals(p.Name, cleanName, StringComparison.OrdinalIgnoreCase));
        // }
        return property;
    }

    private static bool IsNullableValueType(Type type) =>
        type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>);

    private static bool IsGenericList(Type type) =>
        type.IsGenericType && (type.GetGenericTypeDefinition() == typeof(List<>) ||
                            type.GetGenericTypeDefinition() == typeof(IList<>) ||
                            type.GetGenericTypeDefinition() == typeof(ICollection<>));

    private static bool IsPaginationProperty(string propertyName)
    {
        var paginationProperties = new[] { "PageNumber", "PageSize", "SortProperty", "SortType", "SortDirection" };
        return paginationProperties.Contains(propertyName);
    }
}

[AttributeUsage(AttributeTargets.Property)]
public class FilterPropertyAttribute(
    string entityPropertyName = null,
    FilterType filterType = FilterType.Equal
) : Attribute
{
    public string EntityPropertyName { get; set; } = entityPropertyName;
    public FilterType FilterType { get; set; } = filterType;
    public bool IgnoreCase { get; set; } = true;
}

public enum FilterType
{
    Equal,
    Contains,
    StartsWith,
    EndsWith,
    GreaterThan,
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    In
}
