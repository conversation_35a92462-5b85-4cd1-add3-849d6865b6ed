using MediatR;
using Requests.Domain;
using Shared.Application;
using Shared.Application.Extensions;
using Shared.Domain;

namespace Requests.Application.Tickets.ListTickets;

public record ListTicketsQuery : BasePagedQuery, IRequest<PagedResult<TicketListItemDto>>
{
    public string? SearchTerm { get; init; }
    public string? Title { get; init; }
    public string? Code { get; init; }
    public string? Country { get; init; }
    public string? State { get; init; }
    public string? City { get; init; }
    public string? Province { get; init; }
    public string? Detail { get; init; }
    public Guid? CustomerId { get; init; }
    public Guid? CallId { get; init; }
    public Guid? ChatId { get; init; }
    public Guid[]? UserIds { get; init; } = null;
    public TicketType? Type { get; set; }
    public Guid[]? WatchUserIds { get; init; } = null;
    public Guid? TopTicketId { get; init; }
    public PriorityEnum? Priority { get; init; }
    public Guid? StatusId { get; init; }
    public Guid[]? StatusIds { get; init; }
    public Guid? FlowId { get; init; }
    public Guid[]? FlowIds { get; init; }
    public Guid? SubjectId { get; init; }
    public Guid[]? SubjectIds { get; init; }
    public DateTime? StartDate { get; init; }
    public DateTime? EndDate { get; init; }
    public Guid[]? DepartmentIds { get; init; } = null;
    public NodeType[]? NodeTypes { get; set; }
}

// public record ListTicketsQuery : BasePagedQuery, IRequest<PagedResult<TicketListItemDto>>
// {
//     [FilterProperty("Name", FilterType.Contains)]
//     public string? SearchTerm { get; init; }
//     [FilterProperty("Title", FilterType.Contains)]
//     public string? Title { get; init; }
//     [FilterProperty("Code", FilterType.Contains)]
//     public string? Code { get; init; }
//     [FilterProperty("Country", FilterType.Contains)]
//     public string? Country { get; init; }
//     [FilterProperty("State", FilterType.Contains)]
//     public string? State { get; init; }
//     [FilterProperty("City", FilterType.Contains)]
//     public string? City { get; init; }
//     [FilterProperty("Province", FilterType.Contains)]
//     public string? Province { get; init; }
//     [FilterProperty("Detail", FilterType.Contains)]
//     public string? Detail { get; init; }
//     [FilterProperty]
//     public Guid? CustomerId { get; init; }
//     [FilterProperty]
//     public Guid? CallId { get; init; }
//     [FilterProperty]
//     public Guid? ChatId { get; init; }
//     [FilterProperty("UserId", FilterType.In)]
//     public Guid[]? UserIds { get; init; } = null;
//     [FilterProperty]
//     public TicketType? Type { get; set; }
//     [FilterProperty]
//     public Guid? TopTicketId { get; init; }
//     [FilterProperty]
//     public PriorityEnum? Priority { get; init; }
//     [FilterProperty]
//     public Guid? StatusId { get; init; }
//     [FilterProperty("StatusId", FilterType.In)]
//     public Guid[]? StatusIds { get; init; } = null;
//     [FilterProperty]
//     public Guid? FlowId { get; init; }
//     [FilterProperty("FlowId", FilterType.In)]
//     public Guid[]? FlowIds { get; init; }
//     [FilterProperty]
//     public Guid? SubjectId { get; init; }
//     [FilterProperty("SubjectId", FilterType.In)]
//     public Guid[]? SubjectIds { get; init; }
//     [FilterProperty("InsertDate", FilterType.GreaterThanOrEqual)]
//     public DateTime? StartDate { get; init; }
//     [FilterProperty("InsertDate", FilterType.LessThanOrEqual)]
//     public DateTime? EndDate { get; init; }

//     public Guid[]? WatchUserIds { get; init; } = null;
//     public Guid[]? DepartmentIds { get; init; } = null;
//     public NodeType[]? NodeTypes { get; set; }
// }
