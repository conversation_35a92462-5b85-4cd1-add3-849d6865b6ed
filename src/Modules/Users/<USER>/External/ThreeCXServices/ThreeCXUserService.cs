using System.Net.Http.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Application;
using Shared.Domain;
using Users.Application.Abstractions;
using Users.Domain.Account;

namespace Users.Infrastructure.External.ThreeCXServices;

public class ThreeCXUserService(
    IUserDbContext dbContext,
    IOptionsSnapshot<AppSettings> appSettings,
    ILogger<ThreeCXUserService> logger
) : IThreeCXUserService
{
    private readonly AppSettings _appSettings = appSettings.Value;

    public async Task<Result> CreateUser(Guid userId, string Password)
    {
        logger.LogError($"3CX Kullanıcı oluşturma işlemi (Servis): {userId} , {Password}");
        if (_appSettings.UseThreeCX == false)
        {
            return Result.Success();
        }
        var tokenResult = await GetAuthTokenAsync();
        if (!tokenResult.IsSuccess)
        {
            return Result.Failure(tokenResult.Error);
        }
        var user = await dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null)
        {
            return Result.Failure("User not found.");
        }
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var requestData = new
        {
            number = user.ThreeCXExtension,
            firstName = user.Name,
            lastName = user.Surname,
            emailAddress = user.Email,
            sendEmailMissedCalls = true
        };
        var response = await client.PostAsJsonAsync($"{apiUrl}/api/Extension/CreateUser", requestData);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure($"Failed to create 3CX user: {error}");
        }
        return Result.Success();
    }

    public async Task<PagedResult<ThreeCXUser>> GetUsers()
    {
        if (_appSettings.UseThreeCX == false)
        {
            return null;
        }
        var tokenResult = await GetAuthTokenAsync();
        if (!tokenResult.IsSuccess)
        {
            return PagedResult<ThreeCXUser>.Failure<ThreeCXUser>(tokenResult?.Error);
        }
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var response = await client.GetAsync($"{apiUrl}/api/Extension/GetUsers");
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return PagedResult<ThreeCXUser>.Failure($"Failed to get 3CX users: {error}");
        }
        var users = await response.Content.ReadFromJsonAsync<List<ThreeCXUser>>();
        return PagedResult<ThreeCXUser>.Success(users);
    }

    public async Task<Result> DeleteUser(string extension)
    {
        if (_appSettings.UseThreeCX == false)
        {
            return Result.Success();
        }
        var getAllUser = await GetUsers();
        if (!getAllUser.IsSuccess)
        {
            return Result.Failure(getAllUser.Error);
        }
        var user = getAllUser.Value.FirstOrDefault(x => x.Number == extension);
        if (user == null)
        {
            return Result.Success();
        }
        var tokenResult = await GetAuthTokenAsync();
        if (!tokenResult.IsSuccess)
        {
            return Result.Failure(tokenResult.Error);
        }
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var requestData = new { ids = new List<string> { extension } };
        var response = await client.PostAsJsonAsync($"{apiUrl}/sdk-operations/users/BatchDelete", requestData);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure($"Failed to delete 3CX user: {error}");
        }
        return Result.Success();
    }

    public async Task<Result> SetQueueStatus(string extension, QueueStatus status, CancellationToken cancellationToken)
    {
        if (_appSettings.UseThreeCX == false)
        {
            return Result.Success();
        }
        var tokenResult = await GetAuthTokenAsync();
        if (!tokenResult.IsSuccess)
        {
            return Result.Failure(tokenResult.Error);
        }
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var requestData = new
        {
            agentId = extension,
            qStatus = status
        };
        var response = await client.PostAsJsonAsync($"{apiUrl}/api/Control/changeAgentQueueStatus", requestData, cancellationToken: cancellationToken);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync(cancellationToken);
            return Result.Failure($"Failed to create 3CX user: {error}");
        }
        return Result.Success();
    }

    public async Task<Result> UpdateUser(User user)
    {
        if (_appSettings.UseThreeCX == false)
        {
            return Result.Success();
        }
        var tokenResult = await GetAuthTokenAsync();
        if (!tokenResult.IsSuccess)
        {
            return Result.Failure(tokenResult.Error);
        }
        var apiUrl = _appSettings.ThreeCXApiUrl;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenResult.Value);
        var requestData = new
        {
            num = user.ThreeCXExtension,
            enabled = user.ThreeCXEnabled,
            external = user.ThreeCXExternal,
            recording = user.ThreeCXRecording
        };
        var response = await client.PostAsJsonAsync($"{apiUrl}/api/Extension/UpdateRecordingSettings", requestData);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure($"Failed to create 3CX user: {error}");
        }
        return Result.Success();
    }

    private async Task<Result<string>> GetAuthTokenAsync()
    {
        using var client = new HttpClient();
        var loginData = new
        {
            username = _appSettings.ThreeCXApiUsername,
            password = _appSettings.ThreeCXApiPassword
        };

        var response = await client.PostAsJsonAsync($"{_appSettings.ThreeCXApiUrl}/api/Auth/Login", loginData);

        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return Result.Failure<string>($"Failed to authenticate with 3CX API: {error}.");
        }

        var tokenResponse = await response.Content.ReadFromJsonAsync<TokenResponse>();
        return tokenResponse?.Token ?? Result.Failure<string>("Token not found in response");
    }

    private class TokenResponse
    {
        public string Token { get; set; } = null!;
    }

    private class ThreeCXUserResponse
    {

    }
}
