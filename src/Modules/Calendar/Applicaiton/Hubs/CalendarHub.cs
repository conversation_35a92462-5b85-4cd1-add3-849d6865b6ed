using Microsoft.AspNetCore.SignalR;

namespace Calendar.Applicaiton.Hubs;

public class CalendarHub : Hub
{
    public async Task AutoCallStarted(Guid userId, string phoneNumber, string detail, Guid autoCallId)
    {
        await Clients.User(userId.ToString()).SendAsync("AutoCallStarted", new
        {
            PhoneNumber = phoneNumber,
            Detail = detail,
            CalendarNoteId = autoCallId
        });
    }
}
