using Calendar.Domain;
using MediatR;
using Shared.Application;

namespace Calendar.Application.CalendarNotes.List;

public record ListCalendarNotesQuery(
    DateTime? StartDate = null,
    DateTime? EndDate = null,
    string? UserIds = null, // Comma-separated GUIDs for Swagger compatibility
    string? DepartmentIds = null, // Comma-separated GUIDs for Swagger compatibility
    string? TagNames = null, // Comma-separated tag names for Swagger compatibility
    string? Title = null,
    string? Types = null, // Comma-separated CalendarNoteType values for Swagger compatibility
    CalendarVisibility? Visibility = null,
    bool? IsImportant = null,
    bool? IsDone = null,
    bool? IsCancelled = null,
    bool? IsRecurring = null,
    string? AttendeeUserIds = null, // Comma-separated GUIDs for Swagger compatibility
    Guid? CreatedByUserId = null,
    string? RelatedCustomerPhone = null,
    Guid? RelatedCustomerId = null,
    Guid? RelatedCompanyId = null,
    int PageNumber = 1,
    int PageSize = 20,
    string? SortBy = "StartDate",
    string? SortDirection = "desc"
) : IRequest<Result<PagedResult<CalendarNoteDto>>>
{
    // Helper properties to parse comma-separated strings back to lists
    public List<Guid> ParsedUserIds => ParseGuids(UserIds);
    public List<Guid> ParsedDepartmentIds => ParseGuids(DepartmentIds);
    public List<string> ParsedTagNames => ParseStrings(TagNames);
    public List<CalendarNoteType> ParsedTypes => ParseEnums<CalendarNoteType>(Types);
    public List<Guid> ParsedAttendeeUserIds => ParseGuids(AttendeeUserIds);

    private static List<Guid> ParseGuids(string? input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return new List<Guid>();
        }

        return input.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Where(s => Guid.TryParse(s.Trim(), out _))
            .Select(s => Guid.Parse(s.Trim()))
            .ToList();
    }

    private static List<string> ParseStrings(string? input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return new List<string>();
        }

        return input.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(s => s.Trim())
            .Where(s => !string.IsNullOrEmpty(s))
            .ToList();
    }

    private static List<T> ParseEnums<T>(string? input) where T : struct, Enum
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return new List<T>();
        }

        return input.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(s => s.Trim())
            .Where(s => Enum.TryParse<T>(s, true, out _))
            .Select(s => Enum.Parse<T>(s, true))
            .ToList();
    }
}
