using Calendar.Applicaiton.Hubs;
using Calendar.Application.Abstractions;
using Calendar.Domain;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Calendar.Infrastructure.BackgroundServices;

public class AutoCallService(
    IServiceScopeFactory scopeFactory,
    IHubContext<CalendarHub> hubContext,
    ILogger<AutoCallService> logger
) : BackgroundService
{

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessAutoCall(stoppingToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while processing expired auto dialers");
            }

            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        }
    }

    private async Task ProcessAutoCall(CancellationToken cancellationToken)
    {
        using var scope = scopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ICalendarDbContext>();

        var expiredAutoCalls = await dbContext.CalendarNotes
            .Where(p => p.Type == CalendarNoteType.CallAuto && p.StartDate <= DateTime.Now && p.IsDone != true)
            .ToListAsync(cancellationToken);

        foreach (var autoCall in expiredAutoCalls)
        {
            try
            {
                if (autoCall.AssignedUserId.HasValue)
                {
                    await hubContext
                    .Clients
                    .User(autoCall.AssignedUserId.Value.ToString())
                    .SendAsync("AutoCallStarted", new
                    {
                        PhoneNumber = autoCall.RelatedCustomerPhone,
                        Detail = autoCall.Description,
                        CalendarNoteId = autoCall.Id
                    }, cancellationToken: cancellationToken);
                    logger.LogInformation(
                        "Auto-started call {RelatedCustomerPhone} for user {AssignedUserId}",
                        autoCall.RelatedCustomerPhone,
                        autoCall.AssignedUserId);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex,
                    "Failed to auto-start autocall {AutoCallId}",
                    autoCall.Id);
            }
        }
    }


}
