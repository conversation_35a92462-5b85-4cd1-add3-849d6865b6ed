using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace General.Application.Resources.GetResourceList;

internal sealed class GetResourceListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/general/resources", async (
            [AsParameters] GetResourceListQuery query,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.Resources")
        .WithGroupName("apiv1")
        .Produces<Result<PagedResult<ResourceListItemDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("General.Resources");

        app.MapGet("/api/v1/general/resources/by-language/{languageCode}", async (
            string languageCode,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetResourceListQuery { LanguageCode = languageCode, PageSize = -1 };
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.Resources")
        .WithGroupName("apiv1")
        .Produces<Result<Dictionary<string, ResourceKeyValueDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .AllowAnonymous();
    }
}
