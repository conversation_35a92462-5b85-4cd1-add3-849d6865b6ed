using FluentValidation;

namespace General.Application.Settings.AddOrUpdateSetting;

public class AddOrUpdateSettingCommandValidator : AbstractValidator<AddOrUpdateSettingCommand>
{
    public AddOrUpdateSettingCommandValidator()
    {
        RuleFor(x => x.Key)
            .NotEmpty().WithMessage("Ayar anahtarı boş olamaz.")
            .MaximumLength(200).WithMessage("Ayar anahtarı en fazla 200 karakter olabilir.");

        RuleFor(x => x.Section)
            .NotEmpty().WithMessage("<PERSON>yar bölümü boş olamaz.")
            .MaximumLength(100).WithMessage("<PERSON>yar bölümü en fazla 100 karakter olabilir.");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Açıklama en fazla 500 karakter olabilir.");
    }
}
