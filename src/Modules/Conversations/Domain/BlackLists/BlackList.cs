using Shared.Domain;

namespace Conversations.Domain.BlackLists;

public class BlackList(
    BlackListContactType contactType,
    string contactValue,
    BlockedChannels blockedChannels,
    Guid? customerId = null,
    string? reason = null,
    DateTime? expiryDate = null
) : AuditableEntity
{
    public Guid Id { get; set; }
    public BlackListContactType ContactType { get; private set; } = contactType;
    public string ContactValue { get; private set; } = contactValue.Trim().ToLowerInvariant();
    public Guid? CustomerId { get; private set; } = customerId;
    public string? Reason { get; private set; } = reason;
    public bool IsActive { get; private set; } = true;
    public DateTime? ExpiryDate { get; private set; } = expiryDate;
    public BlockedChannels BlockedChannels { get; private set; } = blockedChannels;

    public void UpdateContactInfo(BlackListContactType contactType, string contactValue)
    {
        ContactType = contactType;
        ContactValue = contactValue.Trim().ToLowerInvariant();
    }

    public void UpdateReason(string? reason)
    {
        Reason = reason;
    }

    public void UpdateExpiryDate(DateTime? expiryDate)
    {
        ExpiryDate = expiryDate;
    }

    public void UpdateBlockedChannels(BlockedChannels blockedChannels)
    {
        BlockedChannels = blockedChannels;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public bool IsExpired()
    {
        return ExpiryDate.HasValue && ExpiryDate.Value < DateTime.UtcNow;
    }

    public bool IsBlocked(BlockedChannels channel)
    {
        if (!IsActive || IsExpired())
        {
            return false;
        }

        return BlockedChannels.HasFlag(channel);
    }
}
