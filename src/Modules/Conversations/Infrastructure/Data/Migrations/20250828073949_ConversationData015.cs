﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Conversations.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class ConversationData015 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BlackLists",
                schema: "Conversations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ContactType = table.Column<int>(type: "int", nullable: false),
                    ContactValue = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Reason = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    ExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    BlockedChannels = table.Column<int>(type: "int", nullable: false),
                    InsertDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    InsertUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdateUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    History = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BlackLists", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BlackLists_ContactValue",
                schema: "Conversations",
                table: "BlackLists",
                column: "ContactValue");

            migrationBuilder.CreateIndex(
                name: "IX_BlackLists_CustomerId",
                schema: "Conversations",
                table: "BlackLists",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_BlackLists_IsActive_ContactType",
                schema: "Conversations",
                table: "BlackLists",
                columns: new[] { "IsActive", "ContactType" });

            migrationBuilder.CreateIndex(
                name: "IX_BlackLists_IsActive_ExpiryDate",
                schema: "Conversations",
                table: "BlackLists",
                columns: new[] { "IsActive", "ExpiryDate" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BlackLists",
                schema: "Conversations");
        }
    }
}
