using Conversations.Domain.BlackLists;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Conversations.Infrastructure.Data.Configurations;

public class BlackListConfiguration : IEntityTypeConfiguration<BlackList>
{
    public void Configure(EntityTypeBuilder<BlackList> builder)
    {
        builder.ToTable("BlackLists", "Conversations");

        builder.HasKey(x => x.Id);
        
        builder.Property(x => x.ContactType)
            .IsRequired()
            .HasConversion<int>();
            
        builder.Property(x => x.ContactValue)
            .IsRequired()
            .HasMaxLength(255);
            
        builder.Property(x => x.CustomerId);
        
        builder.Property(x => x.Reason)
            .HasMaxLength(500);
            
        builder.Property(x => x.IsActive)
            .IsRequired()
            .HasDefaultValue(true);
            
        builder.Property(x => x.ExpiryDate);
        
        builder.Property(x => x.BlockedChannels)
            .IsRequired()
            .HasConversion<int>();

        // Indexes
        builder.HasIndex(x => x.ContactValue)
            .HasDatabaseName("IX_BlackLists_ContactValue");
            
        builder.HasIndex(x => x.CustomerId)
            .HasDatabaseName("IX_BlackLists_CustomerId");
            
        builder.HasIndex(x => new { x.IsActive, x.ContactType })
            .HasDatabaseName("IX_BlackLists_IsActive_ContactType");
            
        builder.HasIndex(x => new { x.IsActive, x.ExpiryDate })
            .HasDatabaseName("IX_BlackLists_IsActive_ExpiryDate");
    }
}
