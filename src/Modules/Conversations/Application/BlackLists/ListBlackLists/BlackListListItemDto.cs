using Conversations.Domain.BlackLists;

namespace Conversations.Application.BlackLists.ListBlackLists;

public record BlackListListItemDto(
    Guid Id,
    BlackListContactType ContactType,
    string ContactValue,
    Guid? CustomerId,
    string? CustomerName,
    string? Reason,
    bool IsActive,
    DateTime? ExpiryDate,
    BlockedChannels BlockedChannels,
    DateTime InsertDate,
    bool IsExpired
);
