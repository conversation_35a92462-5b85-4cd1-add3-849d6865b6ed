using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.Extensions;

namespace Conversations.Application.BlackLists.ListBlackLists;

public class ListBlackListsQueryHandler(
    IConversationDbContext context
) : IRequestHandler<ListBlackListsQuery, PagedResult<BlackListListItemDto>>
{
    public async Task<PagedResult<BlackListListItemDto>> Handle(
        ListBlackListsQuery request,
        CancellationToken cancellationToken)
    {
        var query = context.BlackLists.AsQueryable();

        // Search term filtering
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower().Trim();
            query = query.Where(x =>
                x.ContactValue.ToLower().Contains(searchTerm) ||
                x.Reason != null && x.Reason.ToLower().Contains(searchTerm));
        }

        // Contact type filtering
        if (request.ContactType.HasValue)
        {
            query = query.Where(x => x.ContactType == request.ContactType.Value);
        }

        // Blocked channels filtering
        if (request.BlockedChannels.HasValue)
        {
            query = query.Where(x => (x.BlockedChannels & request.BlockedChannels.Value) != 0);
        }

        // Active status filtering
        if (request.IsActive.HasValue)
        {
            query = query.Where(x => x.IsActive == request.IsActive.Value);
        }

        // Customer filtering
        if (request.CustomerId.HasValue)
        {
            query = query.Where(x => x.CustomerId == request.CustomerId.Value);
        }

        // Expired filtering
        if (request.IsExpired.HasValue)
        {
            var now = DateTime.UtcNow;
            query = request.IsExpired.Value
                ? query.Where(x => x.ExpiryDate.HasValue && x.ExpiryDate.Value < now)
                : query.Where(x => !x.ExpiryDate.HasValue || x.ExpiryDate.Value >= now);
        }

        // Get counts
        var totalCount = await context.BlackLists.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply sorting and pagination
        var items = await query
            .ApplySorting(request.SortProperty, request.SortType)
            .ApplyPaging(request.PageNumber, request.PageSize)
            .Select(x => new BlackListListItemDto(
                x.Id,
                x.ContactType,
                x.ContactValue,
                x.CustomerId,
                null, // CustomerName - can be joined if needed
                x.Reason,
                x.IsActive,
                x.ExpiryDate,
                x.BlockedChannels,
                x.InsertDate,
                x.ExpiryDate.HasValue && x.ExpiryDate.Value < DateTime.UtcNow))
            .ToListAsync(cancellationToken);

        var result = PagedResult<BlackListListItemDto>.Success(items);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;
        result.SortProperty = request.SortProperty ?? "InsertDate";
        result.SortType = request.SortType ?? "desc";

        return result;
    }
}
