using Conversations.Domain.BlackLists;
using MediatR;
using Shared.Application;
using Shared.Domain;

namespace Conversations.Application.BlackLists.ListBlackLists;

public record ListBlackListsQuery : BasePagedQuery, IRequest<PagedResult<BlackListListItemDto>>
{
    public string? SearchTerm { get; init; }
    public BlackListContactType? ContactType { get; init; }
    public BlockedChannels? BlockedChannels { get; init; }
    public bool? IsActive { get; init; }
    public Guid? CustomerId { get; init; }
    public bool? IsExpired { get; init; }
}
