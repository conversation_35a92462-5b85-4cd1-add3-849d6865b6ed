using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Conversations.Application.BlackLists.ListBlackLists;

internal sealed class ListBlackListsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/conversations/blacklist", async (
            [AsParameters] ListBlackListsQuery query,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Conversations.BlackList")
        .WithGroupName("apiv1")
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .Produces<PagedResult<BlackListListItemDto>>(StatusCodes.Status200OK)
        .RequireAuthorization("Conversations.BlackList");
    }
}
