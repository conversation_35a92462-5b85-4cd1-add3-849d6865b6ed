using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.BlackLists.DeleteBlackList;

public class DeleteBlackListCommandHandler(
    IConversationDbContext context
) : IRequestHandler<DeleteBlackListCommand, Result>
{
    public async Task<Result> Handle(
        DeleteBlackListCommand request,
        CancellationToken cancellationToken)
    {
        var blackList = await context.BlackLists
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (blackList == null)
        {
            return Result.Failure("BlackList kaydı bulunamadı");
        }
        context.BlackLists.Remove(blackList);
        await context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
