using Conversations.Domain.BlackLists;
using MediatR;
using Shared.Application;

namespace Conversations.Application.BlackLists.UpdateBlackList;

public record UpdateBlackListCommand(
    Guid Id,
    BlackListContactType ContactType,
    string ContactValue,
    BlockedChannels BlockedChannels,
    Guid? CustomerId = null,
    string? Reason = null,
    DateTime? ExpiryDate = null,
    bool IsActive = true
) : IRequest<Result>;
