using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.BlackLists.UpdateBlackList;

public class UpdateBlackListCommandHandler(IConversationDbContext context)
    : IRequestHandler<UpdateBlackListCommand, Result>
{
    public async Task<Result> Handle(
        UpdateBlackListCommand request,
        CancellationToken cancellationToken)
    {
        var blackList = await context.BlackLists
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (blackList == null)
        {
            return Result.Failure("BlackList kaydı bulunamadı");
        }

        blackList.UpdateContactInfo(request.ContactType, request.ContactValue);
        blackList.UpdateReason(request.Reason);
        blackList.UpdateExpiryDate(request.ExpiryDate);
        blackList.UpdateBlockedChannels(request.BlockedChannels);

        if (request.IsActive)
        {
            blackList.Activate();
        }
        else
        {
            blackList.Deactivate();
        }

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
