using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Conversations.Application.BlackLists.UpdateBlackList;

internal sealed class UpdateBlackListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/conversations/blacklist", async (
            UpdateBlackListCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Conversations.BlackList")
        .WithGroupName("apiv1")
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .Produces<Result>(StatusCodes.Status204NoContent)
        .RequireAuthorization("Conversations.BlackList");
    }
}
