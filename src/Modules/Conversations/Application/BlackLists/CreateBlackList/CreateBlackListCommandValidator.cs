using System.Text.RegularExpressions;
using FluentValidation;

namespace Conversations.Application.BlackLists.CreateBlackList;

public class CreateBlackListCommandValidator : AbstractValidator<CreateBlackListCommand>
{
    public CreateBlackListCommandValidator()
    {
        RuleFor(x => x.ContactValue)
            .NotEmpty()
            .WithMessage("İletişim bilgisi boş olamaz")
            .MaximumLength(255)
            .WithMessage("İletişim bilgisi 255 karakterden uzun olamaz");

        RuleFor(x => x.ContactValue)
            .Must(BeValidEmailOrPhone)
            .WithMessage("Geçerli bir email adresi veya telefon numarası giriniz");

        RuleFor(x => x.ExpiryDate)
            .GreaterThan(DateTime.UtcNow)
            .When(x => x.ExpiryDate.HasValue)
            .WithMessage("Son tarih gelecek bir tarih olmalıdır");

        RuleFor(x => x.Reason)
            .MaximumLength(500)
            .When(x => !string.IsNullOrEmpty(x.Reason))
            .WithMessage("Engelleme nedeni 500 karakterden uzun olamaz");

        RuleFor(x => x.ContactType)
            .IsInEnum()
            .WithMessage("Geçerli bir iletişim türü seçiniz");

        RuleFor(x => x.BlockedChannels)
            .IsInEnum()
            .WithMessage("Geçerli bir engelleme kanalı seçiniz");
    }

    private bool BeValidEmailOrPhone(string contactValue)
    {
        return IsValidEmail(contactValue) || IsValidPhone(contactValue);
    }

    private bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return false;
        }
        var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
        return emailRegex.IsMatch(email);
    }

    private bool IsValidPhone(string phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
        {
            return false;
        }
        var cleanPhone = phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Replace("+", "");
        return cleanPhone.All(char.IsDigit) && cleanPhone.Length >= 10 && cleanPhone.Length <= 15;
    }
}
