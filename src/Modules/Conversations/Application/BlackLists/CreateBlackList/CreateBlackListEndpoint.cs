using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Conversations.Application.BlackLists.CreateBlackList;

internal sealed class CreateBlackListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/blacklist", async (
            CreateBlackListCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/conversations/blacklist/{id}", new { id }),
                CustomResults.Problem);
        })
        .WithTags("Conversations.BlackList")
        .WithGroupName("apiv1")
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .Produces<Result<Guid>>(StatusCodes.Status201Created)
        .RequireAuthorization("Conversations.BlackList");
    }
}
