using Conversations.Application.Abstractions;
using Conversations.Domain.BlackLists;
using MediatR;
using Shared.Application;

namespace Conversations.Application.BlackLists.CreateBlackList;

public class CreateBlackListCommandHandler(
    IConversationDbContext context
) : IRequestHandler<CreateBlackListCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(
        CreateBlackListCommand request,
        CancellationToken cancellationToken)
    {
        var blackList = new BlackList(
            request.ContactType,
            request.ContactValue,
            request.BlockedChannels,
            request.CustomerId,
            request.Reason,
            request.ExpiryDate
        );
        context.BlackLists.Add(blackList);
        await context.SaveChangesAsync(cancellationToken);
        return Result.Success(blackList.Id);
    }
}
