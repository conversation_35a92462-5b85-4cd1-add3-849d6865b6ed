using Conversations.Domain.BlackLists;
using MediatR;
using Shared.Application;

namespace Conversations.Application.BlackLists.CheckBlackList;

public record CheckBlackListQuery(
    string ContactValue,
    BlackListContactType ContactType,
    BlockedChannels Channel
) : IRequest<Result<CheckBlackListResponse>>;

public record CheckBlackListResponse(
    bool IsBlocked,
    string? Reason,
    DateTime? ExpiryDate,
    BlockedChannels BlockedChannels
);
