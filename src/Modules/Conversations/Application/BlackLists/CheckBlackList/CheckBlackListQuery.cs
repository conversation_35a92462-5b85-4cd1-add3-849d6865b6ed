using Conversations.Domain.BlackLists;
using MediatR;
using Shared.Application;

namespace Conversations.Application.BlackLists.CheckBlackList;

public record CheckBlackListQuery(
    string ContactValue,
    BlackListContactType ContactType
) : IRequest<Result<List<CheckBlackListResponse>>>;

public record CheckBlackListResponse(
    bool IsBlocked,
    string? Reason,
    DateTime? ExpiryDate,
    BlockedChannels BlockedChannels
);
