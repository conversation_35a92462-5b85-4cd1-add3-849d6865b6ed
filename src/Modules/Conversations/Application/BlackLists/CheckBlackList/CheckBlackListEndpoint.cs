using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Conversations.Application.BlackLists.CheckBlackList;

internal sealed class CheckBlackListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/blacklist/check", async (
            CheckBlackListQuery query,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Conversations.BlackList")
        .WithGroupName("apiv1")
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .Produces<Result<CheckBlackListResponse>>(StatusCodes.Status200OK)
        .RequireAuthorization("Conversations.BlackList");
    }
}
