using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.BlackLists.CheckBlackList;

public class CheckBlackListQueryHandler(
    IConversationDbContext context
) : IRequestHandler<CheckBlackListQuery, Result<CheckBlackListResponse>>
{
    public async Task<Result<CheckBlackListResponse>> Handle(
        CheckBlackListQuery request,
        CancellationToken cancellationToken)
    {
        var normalizedContactValue = request.ContactValue.Trim().ToLowerInvariant();
        var now = DateTime.UtcNow;

        var blackListEntry = await context.BlackLists
            .Where(x => x.ContactValue == normalizedContactValue &&
                    x.IsActive &&
                    (!x.ExpiryDate.HasValue || x.ExpiryDate.Value > now) &&
                    (x.ContactType == request.ContactType || x.ContactType == Domain.BlackLists.BlackListContactType.Both))
            .FirstOrDefaultAsync(cancellationToken);

        if (blackListEntry == null)
        {
            return new CheckBlackListResponse(
                IsBlocked: false,
                Reason: null,
                ExpiryDate: null,
                BlockedChannels: 0
            );
        }

        var isChannelBlocked = blackListEntry.IsBlocked(request.Channel);

        return new CheckBlackListResponse(
            IsBlocked: isChannelBlocked,
            Reason: blackListEntry.Reason,
            ExpiryDate: blackListEntry.ExpiryDate,
            BlockedChannels: blackListEntry.BlockedChannels
        );
    }
}
