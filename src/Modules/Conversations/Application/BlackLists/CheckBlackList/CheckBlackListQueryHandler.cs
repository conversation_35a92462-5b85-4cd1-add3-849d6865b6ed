using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.BlackLists.CheckBlackList;

public class CheckBlackListQueryHandler(
    IConversationDbContext context
) : IRequestHandler<CheckBlackListQuery, Result<List<CheckBlackListResponse>>>
{
    public async Task<Result<List<CheckBlackListResponse>>> Handle(
        CheckBlackListQuery request,
        CancellationToken cancellationToken)
    {
        var normalizedContactValue = request.ContactValue.Trim().ToLowerInvariant();
        var now = DateTime.Now;

        var blackLists = await context.BlackLists
            .Where(x => x.ContactValue == normalizedContactValue &&
                    x.IsActive &&
                    (!x.ExpiryDate.HasValue || x.ExpiryDate.Value > now))
            .ToListAsync(cancellationToken);
        var checkBlackListResponseList = new List<CheckBlackListResponse>();
        if (blackLists?.Count == 0)
        {
            return checkBlackListResponseList;
        }
        foreach (var blackList in blackLists)
        {
            var isChannelBlocked = blackList.IsBlocked(blackList.BlockedChannels);

            checkBlackListResponseList.Add(new CheckBlackListResponse(
                IsBlocked: isChannelBlocked,
                Reason: blackList.Reason,
                ExpiryDate: blackList.ExpiryDate,
                BlockedChannels: blackList.BlockedChannels
            ));
        }
        return Result.Success(checkBlackListResponseList);
    }
}
