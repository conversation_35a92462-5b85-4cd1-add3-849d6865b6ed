using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.BlackLists.GetBlackList;

public class GetBlackListQueryHandler(IConversationDbContext context)
    : IRequestHandler<GetBlackListQuery, Result<BlackListDto>>
{
    public async Task<Result<BlackListDto>> Handle(
        GetBlackListQuery request,
        CancellationToken cancellationToken)
    {
        var blackList = await context.BlackLists
            .Where(x => x.Id == request.Id)
            .Select(x => new BlackListDto(
                x.Id,
                x.ContactType,
                x.ContactValue,
                x.CustomerId,
                x.Reason,
                x.IsActive,
                x.ExpiryDate,
                x.BlockedChannels,
                x.InsertDate,
                x.UpdateDate))
            .FirstOrDefaultAsync(cancellationToken);
        if (blackList == null)
        {
            return Result.Failure<BlackListDto>("BlackList kaydı bulunamadı");
        }
        return Result.Success(blackList);
    }
}
