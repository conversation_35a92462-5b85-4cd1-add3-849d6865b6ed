using Conversations.Domain.AutoDialers;
using Conversations.Domain.BlackLists;
using Conversations.Domain.Calls;
using Conversations.Domain.Chats;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Data;

namespace Conversations.Application.Abstractions;

public interface IConversationDbContext : IBaseDbContext
{
    DbSet<Call> Call { get; }
    DbSet<CallNote> CallNote { get; }
    DbSet<Chat> Chat { get; }
    DbSet<ChatMessage> ChatMessage { get; }
    DbSet<ChatAttachment> ChatAttachment { get; }
    DbSet<AutoDialer> AutoDialers { get; }
    DbSet<BlackList> BlackLists { get; }
}
