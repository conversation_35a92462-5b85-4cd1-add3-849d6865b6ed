using Conversations.Application.Abstractions;
using MediatR;
using Shared.Application;

namespace Conversations.Application.ThreeCX.MakeCall;

public class MakeCallCommandHandler(
    IThreeCXService threeCXService
) : IRequestHandler<MakeCallCommand, Result>
{
    public async Task<Result> Handle(MakeCallCommand request, CancellationToken cancellationToken)
    {
        return await threeCXService.MakeCall(request.Extension, request.PhoneNumber);
    }
}
