using FluentValidation;

namespace Conversations.Application.ThreeCX.MakeCall;

public class MakeCallCommandValidator : AbstractValidator<MakeCallCommand>
{
    public MakeCallCommandValidator()
    {
        RuleFor(x => x.Extension)
            .NotEmpty()
            .WithMessage("Extension boş olamaz");

        RuleFor(x => x.PhoneNumber)
            .NotEmpty()
            .WithMessage("Telefon boş olamaz");
    }
}
