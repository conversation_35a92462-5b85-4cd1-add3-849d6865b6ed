using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Conversations.Application.ThreeCX.GetCallHistory;

public class GetCallHistoryEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/conversations/threecx/callhistory/{HistoryOfTheCall}", async (
            string HistoryOfTheCall,
            ISender sender,
            CancellationToken cancellationToken
        ) =>
        {
            var query = new GetCallHistoryQuery(HistoryOfTheCall);
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Conversations.ThreeCX")
        .WithGroupName("apiv1")
        .Produces<Result<List<CallHistoryResponse>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization();
    }
}
