using Conversations.Application.Abstractions;
using MediatR;
using Shared.Application;
using Shared.Contracts;

namespace Conversations.Application.ThreeCX.GetCallHistory;

public class GetCallHistoryQueryHandler(
    IThreeCXService threeCXService,
    ISharedUserService userService,
    IWorkContext workContext
) : IRequestHandler<GetCallHistoryQuery, Result<List<CallHistoryResponse>>>
{

    public async Task<Result<List<CallHistoryResponse>>> Handle(GetCallHistoryQuery request, CancellationToken cancellationToken)
    {
        var result = await threeCXService.GetCallHistory(request.HistoryOfTheCall);
        if (!result.IsSuccess)
        {
            return Result.Failure<List<CallHistoryResponse>>(result.Error.Code, result.Error.Description);
        }
        var response = result.Value.Select(x => new CallHistoryResponse
        {
            CallHistoryId = x.CallHistoryId,
            SourceEntityType = x.SourceEntityType,
            SourceParticipantPhoneNumber = x.SourceParticipantPhoneNumber,
            SourceParticipantName = x.SourceParticipantName,
            SourceParticipantTrunkDid = x.SourceParticipantTrunkDid,
            CreationMethod = x.CreationMethod,
            RecordingUrl = x.RecordingUrl,
            StartTime = x.StartTime,
            EndTime = x.EndTime,
            Transcription = x.Transcription,
            Summary = x.Summary

        }).ToList();
        var userId = workContext.UserId;
        var user = await userService.GetUserAsync(userId);
        var hasPermission = await userService.HasPermissionAsync(user.Value.Id, "Conversations.ThreeCXHistory");
        if (workContext.HasRole("Admin") || hasPermission)
        {
            return Result.Success(response);
        }
        if (result.Value.Any(x => x.SourceParticipantPhoneNumber == user.Value.Extension))
        {
            return Result.Success(response);
        }
        else
        {
            return Result.Failure<List<CallHistoryResponse>>("403", "Unauthorized");
        }
    }
}
