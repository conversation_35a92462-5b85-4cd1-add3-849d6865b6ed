using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Conversations.Application.ThreeCX.GetCallRecording;

public class GetCallRecordingEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/conversations/threecx/recording", async (
            [AsParameters] GetCallRecordingQuery query,
            ISender sender,
            CancellationToken cancellationToken
        ) =>
        {
            var result = await sender.Send(query, cancellationToken);
            return result;
        })
        .WithTags("Conversations.ThreeCX")
        .WithGroupName("apiv1")
        .Produces<Result>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization();
    }
}
