using Conversations.Application.Abstractions;
using Conversations.Application.ThreeCX.GetCallHistory;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Options;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Application.EventDbLogger;
using Shared.Contracts;
using Shared.Domain;

namespace Conversations.Application.ThreeCX.GetCallRecording;

public class GetCallRecordingQueryHandler(
    IOptionsSnapshot<AppSettings> appSettings,
    IWorkContext workContext,
    IEventBus eventBus,
    IThreeCXService threeCXService,
    ISharedUserService userService
) : IRequestHandler<GetCallRecordingQuery, IResult>
{
    private readonly AppSettings _appSettings = appSettings.Value;
    private readonly IWorkContext _workContext = workContext;
    private readonly IEventBus _eventBus = eventBus;

    public async Task<IResult> Handle(GetCallRecordingQuery request, CancellationToken cancellationToken)
    {
        var result = await threeCXService.GetCallHistory(request.HistoryOfTheCall);
        if (!result.IsSuccess)
        {
            throw new ArgumentException("Kayıt bulunamadı");
        }
        if (!result.Value.Any(x => x.RecordingUrl == request.CallRecordingPath))
        {
            throw new ArgumentException("Kayıtlar uyuşmuyor");
        }
        var path = Path.Combine(_appSettings.ThreeCXRecordingPath, request.CallRecordingPath);
        if (!File.Exists(path))
        {
            throw new ArgumentException("Dosya bulunamadı");
        }
        var fileStream = File.OpenRead(path);
        await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
                _workContext.UserId,
                "Conversations",
                0,
                false,
                0,
                request.CallRecordingPath,
                null,
                "GetCallRecordingQueryHandler"
            ), cancellationToken);
        var filename = request.CallRecordingPath.Replace("/", "_");
        var fileProvider = new FileExtensionContentTypeProvider();
        if (!fileProvider.TryGetContentType(request.CallRecordingPath, out string contentType))
        {
            throw new ArgumentOutOfRangeException($"Unable to find Content Type for file name {request.CallRecordingPath}.");
        }
        var userId = workContext.UserId;
        var user = await userService.GetUserAsync(userId);
        var hasPermission = await userService.HasPermissionAsync(user.Value.Id, "Conversations.ThreeCXHistory");
        if (workContext.HasRole("Admin") || hasPermission)
        {
            return Results.Stream(fileStream, contentType, filename);
        }
        if (result.Value.Any(x => x.SourceParticipantPhoneNumber == user.Value.Extension))
        {
            return Results.Stream(fileStream, contentType, filename);
        }
        else
        {
            return Results.Unauthorized();
        }
    }
}
