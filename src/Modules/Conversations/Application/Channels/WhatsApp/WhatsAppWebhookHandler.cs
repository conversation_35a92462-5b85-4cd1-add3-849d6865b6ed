using System.Text.Json;
using Conversations.Application.Abstractions;
using Conversations.Domain.Chats;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Contracts;

namespace Conversations.Application.Channels.WhatsApp;

public class WhatsAppWebhookHandler(
    IMediator mediator,
    ISharedCustomerService sharedCustomerService,
    IMessageSenderFactory messageSenderFactory,
    ISharedFileService sharedFileService,
    IConversationDbContext dbContext,
    ILogger<WhatsAppWebhookHandler> logger)
{
    private readonly IMessageSender _whatsappMessageSender = messageSenderFactory.GetSender(ChatChannel.WhatsApp);

    public async Task<IResult> HandleWebhookAsync(WhatsAppWebhookRequest request)
    {
        try
        {
            if (!await VerifyWebhook())
            {
                logger.LogWarning("Invalid webhook signature");
                return Results.BadRequest("Invalid webhook signature");
            }
            if (request.IsMessageEvent())
            {
                await ProcessMessageEvent(request);
                return Results.Accepted();
            }
            else if (request.IsStatusEvent())
            {
                await ProcessStatusEvent(request);
                return Results.Accepted();
            }

            logger.LogWarning("Unknown webhook event type: {Type}", request.Type);
            return Results.BadRequest("Unknown webhook event type");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing WhatsApp webhook");
            return Results.StatusCode(500);
        }
    }

    private async Task ProcessMessageEvent(WhatsAppWebhookRequest request)
    {
        var blackListResult = await dbContext.BlackLists
            .Where(x =>
                x.ContactValue == request.CustomerPhone &&
                x.IsActive && (!x.ExpiryDate.HasValue || x.ExpiryDate.Value > DateTime.UtcNow))
            .FirstOrDefaultAsync();
        if (blackListResult != null)
        {
            logger.LogError("Customer is in blacklist: {Number}", request.CustomerPhone);
            return;
        }
        var customerResult = await sharedCustomerService.GetCustomerAsync(request.CustomerPhone);
        var customerId = customerResult.IsSuccess ? customerResult.Value?.Id : null;
        if (customerId == Guid.Empty)
        {
            logger.LogWarning("Could not resolve customer for WhatsApp number: {Number}", request.CustomerPhone);
            return;
        }
        List<IncomingAttachment>? attachments = null;
        if (!string.IsNullOrWhiteSpace(request.ContentId))
        {
            var attachment = await _whatsappMessageSender.GetAttachmentAsync(request.ContentId);
            var extension = attachment.ContentType?.Split('/').LastOrDefault();
            var fileName = $"{request.ContentId}.{extension}";
            var file = await sharedFileService.UploadFileAsync(attachment.File, fileName, attachment.ContentType, "whatsapp");
            attachments = [
                new() {
                        FileId = file.FileId,
                        FilePath = file.FilePath,
                        FileName = file.FileName
                    }
            ];
        }
        await mediator.Send(new ProcessIncomingMessageCommand
        {
            Channel = ChatChannel.WhatsApp,
            ExternalChatId = request.ConversationId ?? request.From, // Fallback to sender if no conversation ID
            BaseChatId = request.BaseChatId,
            ExternalMessageId = request.MessageId,
            CustomerId = customerId,
            CustomerName = "~ " + (request.MetaData.TryGetValue("Contacts.Profile.Name", out var name) ? name.ToString() : ""),
            Content = request.Content ?? string.Empty,
            ContentType = request.MapContentType(),
            SentAt = request.Timestamp,
            Sender = request.From,
            MetaData = request.MetaData != null ? JsonSerializer.Serialize(request.MetaData) : null,
            Attachments = attachments
        });
    }

    private async Task ProcessStatusEvent(WhatsAppWebhookRequest request)
    {
        // Update message status
        await mediator.Send(new UpdateMessageStatusCommand
        {
            ExternalMessageId = request.MessageId,
            Status = request.MapStatus(),
            Timestamp = request.Timestamp
        });
    }

    private async Task<bool> VerifyWebhook()
    {
        return true;
        // _httpContextAccessor.HttpContext?.Request.EnableBuffering();
        // using var reader = new StreamReader(
        //     _httpContextAccessor.HttpContext.Request.Body,
        //     encoding: Encoding.UTF8,
        //     detectEncodingFromByteOrderMarks: false,
        //     leaveOpen: true);
        // var body = await reader.ReadToEndAsync();
        // _httpContextAccessor.HttpContext.Request.Body.Position = 0;
        // string signatureHeader = _httpContextAccessor.HttpContext?.Request.Headers["X-Hub-Signature"].ToString();
        // string appSecret = _appSettings.WhatsAppWebhookSecret;
        // if (string.IsNullOrEmpty(signatureHeader) || string.IsNullOrEmpty(appSecret))
        // {
        //     return false;
        // }
        // try
        // {
        //     var signature = signatureHeader.Replace("sha1=", "");
        //     var secretBytes = Encoding.UTF8.GetBytes(appSecret);
        //     var payloadBytes = Encoding.UTF8.GetBytes(body);
        //     using var hmac = new HMACSHA1(secretBytes);
        //     var hash = hmac.ComputeHash(payloadBytes);
        //     var hashString = Convert.ToHexStringLower(hash).Replace("-", "").ToLower();
        //     return hashString == signature;
        // }
        // catch
        // {
        //     return false;
        // }
    }
}
