import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import App from "./App";
import "./index.css";
import { Provider } from "react-redux";
import store from "./store/StoreIndex";
import { QueryClient, QueryClientProvider } from "react-query";

import LoadLangResources from "./apps/Layout/LoadLanguageResources";

const queryClient = new QueryClient();
ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <Provider store={store}>
          <LoadLangResources>

          <App />
          </LoadLangResources>
        </Provider>
      </QueryClientProvider>
    </BrowserRouter>
  </React.StrictMode>
);
