import  { useState, useEffect } from 'react';
import { Dropdown, Image, Menu, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

const GeneralSelectedLang = () => {
  const { i18n } = useTranslation();
  const { Text } = Typography;

  // Başlangıç dili localStorage'dan alınıyor
  const [selectedLang, setSelectedLang] = useState<'TR' | 'EN'>(
    (localStorage.getItem('lang') as 'TR' | 'EN') || 'TR'
  );

  // Dil değişim fonksiyonu
  const handleMenuClick = (e: any) => {
    const lang = e.key as 'TR' | 'EN';
    setSelectedLang(lang);
    i18n.changeLanguage(lang); // i18next dil değiştir
    localStorage.setItem('lang', lang); // Kalıcı hale getir
  };

  // Sayfa ilk açıldığında i18n dili ayarla
  useEffect(() => {
    // i18n.changeLanguage(selectedLang);
  }, [i18n, selectedLang]);

  const menu = (
    <Menu onClick={handleMenuClick} className='' >
      <Menu.Item key="TR">Türkçe</Menu.Item>
      <Menu.Item key="EN">English</Menu.Item>
    </Menu>
  );

  return (
    <Dropdown  overlay={menu} trigger={['click']} >
      <div className=" !bg-[#36353]    !rounded-md !cursor-pointer !flex gap-1 items-center ">
        <Image
        preview={false}
        className='!w-[20px] !h-[20px] !rounded-full'
        src={selectedLang==="TR"?"https://static.vecteezy.com/system/resources/previews/022/110/222/non_2x/turkey-flag-round-shape-png.png":"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTZc1DpoXk-JxZUHBpVSM2d_O9Ozy3zwDQuEw&s"}
        />
        <Text className="!text-xs !text-white">{selectedLang}</Text>
        
      </div>
    </Dropdown>
  );
};

export default GeneralSelectedLang;
