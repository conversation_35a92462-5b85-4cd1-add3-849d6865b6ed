import { FC, useEffect, useState } from "react";
import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { normalizeString } from "@/helpers/TRNormalizedName";
import { MazakaSelect } from "./MazakaSelect";
import { useGetCustomers } from "../Admin/Pages/Customers/ServerSideStates";
import { useDebouncedCallback } from "use-debounce";
import { getCustomerListFilter } from "../Admin/Pages/Customers/Services";

const GeneralCustomerWithSearch: FC<GeneralSelectInputs> = (props) => {
  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 20,

  });
  const customers = useGetCustomers(filter);
  const [resultCustomerData, setResultCustomerData] = useState<any[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(false);

  // İlk gelen veriyi göster
  useEffect(() => {
    if (customers.data?.Value) {
      setResultCustomerData(customers.data.Value);
    }
  }, [customers.data]);

  // Debounce edilen arama fonksiyonu
  const debouncedSearch = useDebouncedCallback((value: string) => {
    if (value?.trim()) {
      setFilter({ ...filter, SearchTerm: value.trim() });
    } else {
      setFilter({ PageNumber: 1, PageSize: 20});
    }
  }, 500);

  // Eğer value varsa, detaylı bilgiyi getir
  const getInitialDataIfHasValue = async () => {
    if (props.value) {
      setIsInitialLoading(true);
      try {
        const response = await getCustomerListFilter({ Id: props.value });
        if (response?.Value?.length) {
          setResultCustomerData(response.Value);
        }
      } catch (error) {
        console.log("Initial customer fetch error", error);
      } finally {
        setIsInitialLoading(false);
      }
    }
  };

  useEffect(() => {
    getInitialDataIfHasValue();
  }, [props.value]);

  return (
    <MazakaSelect
      name={props.name}
      xs={props.xs}
      sm={props.sm}
      md={props.md}
      lg={props.lg}
      xl={props.xl}
      loading={
        customers.isLoading ||
        customers.isFetching ||
        props.isLoading ||
        isInitialLoading
      }
      disabled={props.disabled}
      className={props.className}
      showSearch={true}
      allowClear={props.allowClear || false}
      onSearch={debouncedSearch}
      filterOption={(input: string, option: any) => {
        const normalizedInput = normalizeString(input.toLowerCase().trim());
        const normalizedLabel = normalizeString(
          option.label.toLowerCase().trim()
        );
        return (normalizedLabel ?? "").includes(normalizedInput);
      }}
      label={props.label}
      options={
        resultCustomerData?.map((item: any) => ({
          key: item.Id,
          value: item.Id,
          label: `${item.Name || ""} ${item.Surname || ""}`,
          Phone: item?.Phone,
        })) || []
      }
      placeholder={props.placeholder}
      mode={props.mode}
      onChange={props.onChange}
      value={props.value}
      rules={props.rules}
    />
  );
};

export default GeneralCustomerWithSearch;
