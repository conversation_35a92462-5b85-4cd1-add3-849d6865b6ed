import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { useGetAllSettings } from "@/apps/Settings/ServerSideStates";
import { addSettings } from "@/apps/Settings/Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row,  Input } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const FormContent = () => {
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { t } = useTranslation();
  const settings = useGetAllSettings({ PageNumber: 1, PageSize: 100, });

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let data: any[] = [];

    for (let item in formValues) {
      let obj = {
        Key: item,
        Value: formValues[item],
        Section: "AppSettings",
        Description: "",
      };
      data.push(obj);
    }

    try {
      await Promise.all(data.map((item) => addSettings(item)));
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
    } catch (error: any) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  useEffect(() => {
    if (settings?.data?.Value) {
      const allData = settings?.data?.Value?.Value || {};
      const formattedData = allData.reduce((acc: any, item: any) => {
        acc[`${item.Key}`] = item.Value;
        return acc;
      }, {} as Record<string, string>);
     
      form.setFieldsValue({
        ...formattedData,
      });
    }
  }, [settings.data]);

  return (
    <>
      <MazakaForm
        form={form}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
      >
        <Row>
          <Col xs={24} className="!mt-2">
            <Row className="!px-2" gutter={[10, 10]}>
              <Col xs={24}>
                <Row gutter={[10, 10]}>
                  <MazakaSelect
                    xs={24}
                    xl={8}
                    label={t("settings.smsProvider")}
                    placeholder={t("settings.smsProvider")}
                    name="SmsProvider"
                    options={[
                      { label: "1Telekom", value: "1Telekom" },
                      { label: "GesTelekom", value: "GesTelekom" },
                    ]}
                  />
                    <MazakaInput
                    xs={24}
                    xl={8}
                    label={t("settings.title")}
                    placeholder={t("settings.title")}
                    name="SmsUserBaslik"
                  />
                   
                   
                  <MazakaInput
                    xs={24}
                    xl={8}
                    label={t("settings.smsCompanyCode")}
                    placeholder={t("settings.smsCompanyCode")}
                    name="SmsCompanyCode"
                  />
                  <MazakaInput
                    xs={24}
                    xl={8}
                    label={t("settings.smsUserName")}
                    placeholder={t("settings.smsUserName")}
                    name="SmsUserName"
                  />

                  {/* Password 1 */}
                  <Col xs={24} xl={8}>
                    <Form.Item
                      label={t("settings.smsPassword")}
                      name="SmsPassword"
                    >
                      <Input.Password placeholder={t("settings.smsPassword")} />
                    </Form.Item>
                  </Col>

                  {/* Password 2 */}
                  <Col xs={24} xl={8}>
                    <Form.Item
                      label={t("settings.smsPassword2")}
                      name="SmsPassword2"
                    >
                      <Input.Password
                        placeholder={t("settings.smsPassword2")}
                      />
                    </Form.Item>
                  </Col>
                  <MazakaInput
                    xs={24}
                    xl={12}
                    label={t("settings.smsFastLoginMessage")}
                    placeholder={t("settings.smsFastLoginMessage")}
                    name="SmsFastLoginMessage"
                  />
                   <MazakaInput
                    xs={24}
                    xl={12}
                    label={t("settings.smsPhoneConfirmMessage")}
                    placeholder={t("settings.smsPhoneConfirmMessage")}
                    name="SmsPhoneConfirmMessage"
                  />

                  <Col xs={24}>
                    <MazakaButton
                      htmlType="submit"
                      processType={formActions.submitProcessType}
                      status="save"
                    >
                      {t("settings.save")}
                    </MazakaButton>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default FormContent;
