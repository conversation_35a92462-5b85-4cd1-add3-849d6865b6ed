import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { useGetAllSettings } from "@/apps/Settings/ServerSideStates";
import { addSettings } from "@/apps/Settings/Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Divider, Form, Row, } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const FormContent = () => {
  
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { t } = useTranslation();
   const settings = useGetAllSettings({ PageNumber: 1, PageSize: 100, });
 
   const handleOnFinish = async () => {
     mazakaForm.setLoading();
     let formValues = form.getFieldsValue();
     let data: any[] = [];
 
     for (let item in formValues) {
       let obj = {
         Key: item,
         Value: formValues[item]||"",
         Section: "AppSettings",
         Description: "",
       };
       data.push(obj);
     }
 
     try {
       await Promise.all(data.map((item) => addSettings(item)));
       mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
       openNotificationWithIcon("success", t("form.transactionSuccessful"));
     } catch (error: any) {
       showErrorCatching(error, mazakaForm, true, t);
     }
   };
 
   useEffect(() => {
     if (settings?.data?.Value) {
       const allData = settings?.data?.Value?.Value || {};
       const formattedData = allData.reduce((acc: any, item: any) => {
         acc[`${item.Key}`] = item.Value;
         return acc;
       }, {} as Record<string, string>);
     
       form.setFieldsValue({
         ...formattedData,
       });
     }
   }, [settings.data]);
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
      >
        <Row>
          
         
          <Col xs={24} className="!mt-2">
            <Row className="!px-2" gutter={[10, 10]}>
              <Col xs={24}>
                <Row gutter={[10, 10]}>
                <MazakaInput
                  xs={24}
                  xl={8}
                  label={t("settings.id")}
                  placeholder={t("settings.id")}
                  name="Id"
                  disabled
                />
                <MazakaInput
                  xs={24}
                  xl={8}
                  label={t("settings.title")}
                  placeholder={t("settings.title")}
                  name="Title"
                />
                <MazakaInput
                  xs={24}
                  xl={8}
                  label={t("settings.cacheTime")}
                  placeholder={t("settings.cacheTime")}
                  name="CacheTime"
                  type="number"
                />
                <MazakaInput
                  xs={24}
                  xl={8}
                  label={t("settings.defaultRegion")}
                  placeholder={t("settings.defaultRegion")}
                  name="DefaultRegion"
                />
                <MazakaSelect
                  xs={24}
                  xl={8}
                  label={t("settings.defaultLanguage")}
                  placeholder={t("settings.defaultLanguage")}
                  name="DefaultLanguage"
                  options = {
                    [
                      {label:"TR",value:"tr"},
                      {label:"EN",value:"en"},
                    ]
                  }
                />
                <MazakaInput
                  xs={24}
                  xl={8}
                  label={t("settings.mediaPath")}
                  placeholder={t("settings.mediaPath")}
                  name="MediaPath"
                />
                <MazakaInput
                  xs={24}
                  xl={8}
                  label={t("settings.ticketCodeFormat")}
                  placeholder={t("settings.ticketCodeFormat")}
                  name="TicketCodeFormat"
                />
                <MazakaInput
                  xs={24}
                  xl={8}
                  label={t("settings.taskCodeFormat")}
                  placeholder={t("settings.taskCodeFormat")}
                  name="TaskCodeFormat"
                />
                
                <MazakaInput
                  xs={24}
                  xl={8}
                  label={t("settings.mobilePushAccountFilePath")}
                  placeholder={t("settings.mobilePushAccountFilePath")}
                  name="MobilePushAccountFilePath"
                />
                  <Col xs={24}>
                    <MazakaButton
                      htmlType="submit"
                      processType={formActions.submitProcessType}
                      status="save"
                    >
                      {t("settings.save")}
                    </MazakaButton>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default FormContent;
