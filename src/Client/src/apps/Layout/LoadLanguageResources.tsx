import { FC, useEffect, useState } from "react";
import { useGetResourceByLang } from "../Admin/Pages/Resources/ServerSideStates";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import GeneralPageLoading from "../Common/GeneralPageLoading";

i18n.use(initReactI18next).init({
  resources: {
    tr: { translation: {} },
    en: { translation: {} },
  },
  lng: localStorage.getItem("lang") || "tr",
  fallbackLng: ["tr", "en"],
  interpolation: { escapeValue: false },
});

const LoadLangResources: FC<{ children: any }> = ({ children }) => {
  const defaultLang = "tr";
  const initialLang = localStorage.getItem("lang") || defaultLang;
  const [language, setLanguage] = useState(initialLang);

  // API’den kaynakları al
  const resources = useGetResourceByLang(language.toLowerCase());

  useEffect(() => {
    if (resources?.data?.Value?.Value) {
      const resourceData = resources.data.Value.Value;

      // {Key: Value} formatına çevir
      const formattedResourceData = resourceData.reduce(
        (acc: Record<string, any>, item: any) => {
          acc[item.Key] = item.Value;
          return acc;
        },
        {}
      );

      const finalResource = {
        [language]: { translation: formattedResourceData },
      };

      // i18n’ye yükle
      i18n.addResources(language, "translation", formattedResourceData);

      // localStorage’a kaydet
      localStorage.setItem("langResource", JSON.stringify(finalResource));
    }
  }, [resources?.data, language]);

  // Refresh sonrası localStorage’dan yükle
  useEffect(() => {
    const stored = localStorage.getItem("langResource");
    if (stored) {
      const parsed = JSON.parse(stored);
      Object.keys(parsed).forEach((lng) => {
        i18n.addResources(lng, "translation", parsed[lng].translation);
      });
    }
  }, []);


  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      setLanguage(lng);
      localStorage.setItem("lang", lng);
    };

    i18n.on("languageChanged", handleLanguageChange);
    return () => {
      i18n.off("languageChanged", handleLanguageChange);
    };
  }, []);

  if (resources.isLoading || resources.isFetching) {
    return <GeneralPageLoading />;
  }

  return <>{children}</>;
};

export default LoadLangResources;
