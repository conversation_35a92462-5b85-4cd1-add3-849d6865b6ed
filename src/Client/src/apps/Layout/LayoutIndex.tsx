import { Layout } from "antd";
import { Content } from "antd/es/layout/layout";
import { FC, useEffect, useState } from "react";
import { Navigate, Outlet, useNavigate } from "react-router-dom";
import HeaderIndex from "./Components/Header/HeaderIndex";
import SidebarIndex from "./Components/Sidebar/SidebarIndex";
import { useDispatch, useSelector } from "react-redux";
import { getUserInfoes } from "../Account/Pages/Profile/Services";
import { handleSetProfileInfoes } from "../Account/Pages/Profile/ClientSideStates";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import GeneralPageLoading from "../Common/GeneralPageLoading";
import { RootState } from "@/store/Reducers";
import { useQueryClient } from "react-query";
import menuEndpoints from "@/apps/Layout/Components/Sidebar/EndPoints";
import { useGetAdminMenus } from "./Components/Sidebar/ServerSideStates";


const LayoutIndex: FC<{ wrapperClassNames?: string }> = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const queryClient = useQueryClient();
  const userPermissions = useGetAdminMenus();


  useEffect(() => {
    const getUserInformations = async () => {
      setIsLoading(true);
      try {
        const token = localStorage.getItem("access_token");
        if (token) {
          const response: any = await getUserInfoes();
          if (response?.Value) {
            dispatch(handleSetProfileInfoes({ data: response.Value }));
            queryClient.resetQueries({
              queryKey: menuEndpoints.getAdminMenus,
              exact: false,
            });
          }
        } else {
          localStorage.removeItem("access_token");
          navigate("/account/login");
        }
      } catch (error: any) {
        if (error?.status === 403) {
          navigate("/forbidden");
        } else if (error?.status === 401) {
          const errorMessage =
            error?.Message ||
            error?.Value?.Message ||
            "Kullanıcı Bilgileri çekerken bir hata oluştur";
          openNotificationWithIcon("error", errorMessage);

          localStorage.removeItem("access_token");
          navigate("/account/login");
        }
      } finally {
        setIsLoading(false);
      }
    };

    getUserInformations();
  }, [dispatch]);



 


  const isPageLoading = isLoading || userPermissions.isLoading

  if (isPageLoading) {
    return <GeneralPageLoading />;
  }

  if (!localStorage.getItem("access_token")) {
    return <Navigate to="/account/login" replace />;
  }
  return (
    <>
      <Layout className="">
        <HeaderIndex />
      </Layout>
      <Layout className="!bg-transparent">
        <SidebarIndex />
        <Content
          className={`${
            userInfoes?.Role?.toLowerCase() === "admin" ? "!ml-[70px]" : "!ml-0"
          } !mt-12 !bg-none !overflow-hidden`}
        >
          <Outlet />
        </Content>
      </Layout>
    </>
  );
};

export default LayoutIndex;
