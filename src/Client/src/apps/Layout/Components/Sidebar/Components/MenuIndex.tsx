import { Typography } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, } from "react-router-dom";
import AdminMenuButton from "./AdmnMenuButton";
import { useGetAdminMenus } from "../ServerSideStates";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { allMenuIcons } from "./AllMenuIcons";
import { adminMenuItems } from "./AdminMenuItems";
import { commonRoutePrefix } from "../../../../../routes/Prefix";
import { handleResetAllFieldsChat } from "@/apps/Chat/ClientSideStates";

const MenuIndex = () => {
  const { Text } = Typography;
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const [data, setData] = useState<any[]>([]);
  const menus = useGetAdminMenus();
 const dispatch = useDispatch()


  const findIconByUrl = (url: string) => {

    const findIcon = allMenuIcons.find((item: any) =>{
 
      return  item.url === "/profile/general"|| item.url==="/settings/sms"
      ? item.url === url
      : commonRoutePrefix + item.url === url
    }
     
    );
    if (findIcon) {
      return findIcon?.icon;
    }
  };

  useEffect(() => {
    const menuItems = adminMenuItems(t);

    if (userInfoes?.Role?.toLowerCase() === "admin") {
      
      const fullPathWithParams = location.pathname + location.search;

      const withStatus = menuItems.map((item: any) => {
        let status = item.url === fullPathWithParams;
      
       
        if (fullPathWithParams?.includes("/profile") && item.url === "/profile/general") {
          status = true;
        }
        else if (fullPathWithParams?.includes("type=constant")&& item.url=="/panel/professions?type=constant")  {
          status = true
        }
        if (fullPathWithParams?.includes("/settings") && item.url === "/settings/sms") {
          status = true;
        }
      
        return {
          ...item,
          status,
        };
      });
      setData(withStatus);
    } else {
      if (menus?.data?.Value) {
        const withStatus = [];

        const staticMenus = [menuItems[0], menuItems[2]].map(
          (item: any) => ({
            ...item,
            status: item.url === location.pathname,
          })
        );

        withStatus.push(staticMenus[0]);

        const dynamicMenus = menus?.data?.Value?.filter(
          (item: any) => item.IsMenu
        ).map((item: any) => ({
          title: item.Name,
          url: item.Url,
          icon: item.Icon,
          status: item.Url === location.pathname,
        }))||[];

        if (dynamicMenus?.length>0) {
          withStatus.push(...dynamicMenus);
        }

        withStatus.push(staticMenus[1]);
        setData(withStatus);
      }
    }
  }, [location.pathname, t, userInfoes, menus.data]);

  const handleClick = (url: string) => {
    
    if(url==="/panel/professions?type=constant"){
      localStorage.setItem("selectedParent","jobs")
    }
    else if(url==="/panel/chat")
    {
      dispatch(handleResetAllFieldsChat())
    }
    else if(url==="/profile/general")
    {
      localStorage.setItem("selectedParent","general")
    }
    else if(url==="/settings/sms")
      {
        localStorage.setItem("selectedParent","sms")
      }
    navigate(url);
  };

  return (
    <div className="!flex flex-col !min-h-screen">
      {data.map((item, index) => (
        <div
          key={index}
          className={`!w-full !h-[40px] !flex !flex-col gap-1 items-center justify-center !cursor-pointer  !my-1 ${
            item.status ? "!bg-[#0096d1]" : ""
          }`}
          onClick={() => handleClick(item.url)}
        >
         <>{findIconByUrl(item?.url || item?.Url)}</>

    
            <Text
              className={`!text-[10px] !text-center ${
                item.status ? "!text-white" : "!text-[#b5b5b5]"
              }`}
            >
              {item.title}
            </Text>
        
        </div>
      ))}
      <div className="!absolute bottom-16 !w-full">
        <AdminMenuButton />
      </div>
    </div>
  );
};

export default MenuIndex;
