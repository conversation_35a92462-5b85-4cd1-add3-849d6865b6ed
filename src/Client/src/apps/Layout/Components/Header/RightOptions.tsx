import PauseHub from "@/apps/Pauses/Components/PauseHub";
import CallNotification from "./CallNotification";
import Notifications from "./Notifications";
import UserMenu from "./UserMenu";
// import CallNotification from "./CallNotification";
import GeneralSelectedLang from "@/apps/Common/GeneralChangeLang";
import ChatNotification from "./ChatNotification";
import CalendarHub from "@/apps/Admin/Pages/Calendar/Components/CalendarHub";

const RightOptions = () => {
  return (
    <>

      {/* <PhoneOutlined className="!text-white !text-lg cursor-pointer" /> */}
      {/* <QRIndex/> */}
      <CallNotification />
      <PauseHub />
      <ChatNotification />
      <Notifications />
      <CalendarHub/>

      <GeneralSelectedLang />
      <UserMenu />
    </>
  );
};

export default RightOptions;
