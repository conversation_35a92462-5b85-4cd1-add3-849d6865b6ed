import GeneralDepartments from "@/apps/Common/Departments/GeneralDepartments";
import GeneralCustomerWithSearch from "@/apps/Common/GeneralCustomerWIthSearch";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { useTranslation } from "react-i18next";
import GeneralTags from "../../../Ticket/Components/Tags";
import {
  determineCalendarNoteType,
  determineCalendarVisibility,
  determineRecurrenceType,
  determineReminderChannel,
} from "@/helpers/Calendar";
import { Checkbox, Col, Form, Row } from "antd";
import { MazakaCheckbox } from "@/apps/Common/MazakaCheckbox";
import { FC } from "react";
import { FormInstance } from "antd/lib";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const RightPart: FC<{ form: FormInstance }> = ({ form }) => {
  const { t } = useTranslation();
  const { calendarData } = useSelector((state: RootState) => state.calander);
  const calendarNoteTypes = determineCalendarNoteType("select", null, t);
  const visibilities = determineCalendarVisibility("select", null, t);
  const reminderChannels = determineReminderChannel("select", null, t);
  const recurrenceTypes = determineRecurrenceType("select", null, t);
  return (
    <Row gutter={[20, 20]} className="!bg-[#fafafa] !p-2">
      <MazakaSelect
       name="IsImportant"
       label={t("calendar.priority")}
       placeholder={t("calendar.priority")}
       options={[
        {label:t("calendar.isImportant"),value:"true"},
        {label:t("calendar.standard"),value:"false"},
       ]}
       xs={24}
        rules={[{ required: true, message: "" }]}
      />
      

      <MazakaSelect
        label={t("calendar.noteType")}
        placeholder={t("calendar.noteType")}
        name="Type"
        xs={24}
        options={calendarNoteTypes}
        rules={[{ required: true, message: "" }]}
      />

      <MazakaSelect
        label={t("calendar.recurrenceType")}
        placeholder={t("calendar.recurrenceType")}
        name="RecurrenceTypes"
        xs={24}
        options={recurrenceTypes}
        allowClear
      />
      <MazakaSelect
        label={t("calendar.reminderChannel")}
        placeholder={t("calendar.reminderChannel")}
        name="ReminderChannel"
        xs={24}
        mode="multiple"
        options={reminderChannels}
        rules={[{ required: true, message: "" }]}
      />

      <GeneralCustomerWithSearch
        label={t("calendar.relatedCustomer")}
        placeholder={t("calendar.relatedCustomer")}
        name="RelatedCustomerId"
        xs={24}
        allowClear
      />

      <GeneralDepartments
        label={t("calendar.departments")}
        placeholder={t("calendar.departments")}
        name="DepartmentIds"
        xs={24}
        multiple={true}
        allowClear
      />

      <GeneralUsers
        placeholder={t("calendar.attendingUsers")}
        label={t("calendar.attendingUsers")}
        name="AttendeeUserIds"
        xs={24}
        mode="multiple"
        allowClear
      />

      <GeneralTags
        xs={24}
        label={t("calendar.tags")}
        placeholder={t("calendar.tags")}
        name="TagNames"
        mode="multiple"
        initialData={
          calendarData?.Tags?.map((item: any) => {
            return { Name: item?.TagName, Id: item.TagId };
          }) || []
        }
      />

      <MazakaCheckbox text={t("calendar.public")} name="IsPublic" xs={24} />

      <MazakaSelect
        className="!hidden"
        label={t("calendar.visibilities")}
        placeholder={t("calendar.visibilities")}
        name="Visibility"
        xs={24}
        options={visibilities}
      />
    </Row>
  );
};

export default RightPart;
