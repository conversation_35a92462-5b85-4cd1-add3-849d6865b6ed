import { MazakaForm } from "@/apps/Common/MazakaForm";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { createCalendarNote, updateCalendarNoteWithPut } from "../../Services";
import LeftPart from "./LeftPart";
import RightPart from "./RightPart";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import dayjs from "dayjs";
import { useParams, useSearchParams } from "react-router-dom";

const AddOrUpdateCalendarNotes: FC<{
  onFinish: () => void;
}> = ({ onFinish }) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { t } = useTranslation();
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { calendarData } = useSelector((state: RootState) => state.calander);
    const params = useParams();
    const [searchParams] = useSearchParams();
    const customerId = params["customerId"] || searchParams.get("customerId");

  useEffect(() => {
    if (calendarData) {
      let data = { ...calendarData };
      data["StartDate"] = dayjs(data["StartDate"]);
      data["EndDate"] = dayjs(data["EndDate"]);
      data["DepartmentIds"] = data["Departments"]?.map(
        (item: any) => item.DepartmentId
      );
      data["AttendeeUserIds"] = data["Attendees"]?.map(
        (item: any) => item.UserName
      );
      data["TagNames"] = data["Tags"]?.map((item: any) => item.TagId);
      data["IsPublic"] = data["Visibility"] === 2;
      data["IsImportant"] = data["IsImportant"]?.toString();
      data["RecurrenceTypes"] = data?.Recurrence?.Type;
      data["RecurrenceRecurrenceCount"] = data?.Recurrence?.Interval;
      if (data?.Recurrence?.EndDate) {
        data["RecurrenceExpiryDate"] = dayjs(data?.Recurrence?.EndDate);
      }

      form.setFieldsValue({ ...data });
    }
  }, [calendarData]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();
     
   
    formValues["RelatedCustomerId"] = customerId?customerId:formValues["RelatedCustomerId"]
    if (formValues["DepartmentIds"]?.length > 0) {
      formValues["Visibility"] = 1;
    } else if (formValues["IsPublic"]) {
      formValues["Visibility"] = 2;
    } else {
      formValues["Visibility"] = 0;
    }

    formValues["Reminder"] = formValues["ReminderChannel"].map(
      (channel: number) => {
        return {
          MinutesBefore: 0,
          Channel: channel,
        };
      }
    );
    delete formValues["ReminderChannel"];
  
    if (formValues["RecurrenceTypes"] === 0 || formValues["RecurrenceTypes"]) {
      formValues["Recurrence"] = {
        Type: formValues["RecurrenceTypes"],
        Interval: Number(formValues["RecurrenceCount"]),
        OccurrenceCount: Number(formValues["RecurrenceCount"]),
        EndDate: dayjs(formValues["RecurrenceExpiryDate"]),
      };
      delete formValues["RecurrenceTypes"];
    }
    if (formValues?.TagNames?.length > 0) {
      formValues["TagNames"] = formValues["TagNames"];
    } else {
      formValues["TagNames"] = [];
    }

    formValues["IsImportant"] =
      formValues["IsImportant"] === "true" ? true : false;
 formValues["StartDate"] = dayjs(formValues["StartDate"]).format("YYYY-MM-DDTHH:mm")
    formValues["EndDate"] =  dayjs(formValues["EndDate"]).format("YYYY-MM-DDTHH:mm")
    delete formValues["RecurrenceCount"];
    delete formValues["RecurrenceExpiryDate"];

    try {
      if (calendarData) {
        let data = { ...calendarData };
        delete formValues["AttendeeUserIds"];
        await updateCalendarNoteWithPut({ ...data, ...formValues });
      } else {
        formValues["AssignedUserId"] = userInfoes?.Id;
        await createCalendarNote(formValues);
      }
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getCalendarNotesListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, mazakaForm, false, t);
    }
  };
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
        initialValues={{
          IsRecurring: false,
          IsAllDay: false,
          IsImportant: "false",
          IsPublic: false,
          Type: 0,
          ReminderChannel: [1],
          StartDate: dayjs(),
          EndDate: dayjs().add(1, "hour"),
        }}
      >
        <Row gutter={[20, 20]}>
          <Col xs={24} lg={12} xl={14}>
            <LeftPart formActions={formActions} form={form} />
          </Col>
          <Col xs={24} lg={12} xl={10}>
            <RightPart form={form} />
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateCalendarNotes;
