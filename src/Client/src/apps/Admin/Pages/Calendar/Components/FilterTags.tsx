import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralFilterTags from "../../Users/<USER>/GeneralFilterTags";
import { handleSetCalendarNotesFilter } from "../ClientSideStates";


const FilterTags = () => {
    const {filter} = useSelector((state:RootState)=>state.calander)
    return ( <>
    
    <GeneralFilterTags
    showFilterTagLength={2}
    filter={filter}
    actionFunc={handleSetCalendarNotesFilter}
    actionFunkKey="filter"
    isDontCloseableDate={true}
        excludedKeys={["PageNumber","PageSize","Types","Visibility","IsImportant"]}
    />
    </> );
}
 
export default FilterTags;
