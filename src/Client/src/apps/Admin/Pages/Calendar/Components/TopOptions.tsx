import { <PERSON>, <PERSON>vide<PERSON>, Drawer, <PERSON> } from "antd";
import { useState } from "react";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { ClearOutlined, PlusOutlined } from "@ant-design/icons";

import { useTranslation } from "react-i18next";
import Search from "./Search";
import ViewingModes from "./ViewingModes";
import AddOrUpdateCalendarNotes from "./AddOrUpdate/AddOrUpdateCalenderNotes";
import GeneralFilterButton from "@/apps/Common/GeneralFilterButton";
import DetailsFilter from "./DetailsFilter";
import FilterTags from "./FilterTags"
import { handleResetFilterCalendarNotes, handleSetCalendarData } from "../ClientSideStates";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const TopOptions = () => {
  const [isShowAddDrawer, setIsShowAddDrawer] = useState(false);
  const [isShowDetailsFilterDrawer, setIsShowDetailsFilterDrawer] =
    useState(false);
  const { t } = useTranslation();
  const { filter } = useSelector((state: RootState) => state.calander);
  const dispatch = useDispatch()
  return (
    <>
      <Row>
        <>
          <Col xs={24}>
            <Search />
          </Col>
          <Col xs={24} className="">
            <Divider className="!m-0" />
          </Col>
          <Col xs={24} className=" !py-2 !px-2 !flex gap-1">
            <MazakaButton
              icon={<PlusOutlined />}
              onClick={() => {
                 dispatch(handleSetCalendarData({data:null}))
                setIsShowAddDrawer(true);
              }}
            >
              {t("calendar.add")}
            </MazakaButton>
            <GeneralFilterButton
              selectedIds={[]}
              onClick={() => {
                setIsShowDetailsFilterDrawer(true);
              }}
              title={t("users.filter.filterButton")}
            />
            {filter && Object.entries(filter).length > 4 && (
              <>
                
                  <MazakaButton
       status="save"
        type="text"
        onClick={() => {
          dispatch(handleResetFilterCalendarNotes({filter:filter}));
          
        }}
        icon={<ClearOutlined />}
       
      >
        {t("clearFilterButton")}
      </MazakaButton>
              </>
            )}
          </Col>
          <Col xs={24}>
            <Divider className="!m-0" />
          </Col>
          <FilterTags />
         
          <Col xs={24} className="!px-2">
            <ViewingModes />
          </Col>
        </>
      </Row>
      <Drawer
        title={t("calendar.addCalendarNote")}
        open={isShowAddDrawer}
        onClose={() => {
          setIsShowAddDrawer(false);
        }}
        width={"60%"}
        destroyOnHidden
      >
        <AddOrUpdateCalendarNotes
          onFinish={() => {
            setIsShowAddDrawer(false);
          }}
        />
      </Drawer>
      <Drawer
        title={t("customers.filter.filterData")}
        open={isShowDetailsFilterDrawer}
        onClose={() => {
          setIsShowDetailsFilterDrawer(false);
        }}
        
      >
        <DetailsFilter
          onFinish={() => {
            setIsShowDetailsFilterDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default TopOptions;
