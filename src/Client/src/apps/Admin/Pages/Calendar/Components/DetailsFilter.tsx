import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { handleSetCalendarNotesFilter } from "../ClientSideStates";
import { MazakaRangePicker } from "@/apps/Common/MazakaRangePicker";
import GeneralDepartments from "@/apps/Common/Departments/GeneralDepartments";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import GeneralTags from "../../Ticket/Components/Tags";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import {
  determineCalendarNoteType,
  determineCalendarVisibility,
} from "@/helpers/Calendar";
import GeneralCustomerWithSearch from "@/apps/Common/GeneralCustomerWIthSearch";
import dayjs from "dayjs"

const DetailsFilter: FC<{ onFinish: any }> = ({ onFinish }) => {
  const { filter } = useSelector((state: RootState) => state.calander);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const importantOptions = [
    { label: t("calendar.isImportant"), value: 1 },
    { label: t("calendar.unimportant"), value: 0 },
  ];
  const calendarNoteTypes = determineCalendarNoteType("select", null, t);
  const visibilities = determineCalendarVisibility("select", null, t);
  const { formActions, mazakaForm } = useMazakaForm(form);
  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
 

    // TagNames array handling
    formValues["customNameTagNames"] =
      formValues["customNameTagNames"]?.length > 0
        ? JSON.parse(JSON.stringify(formValues["customNameTagNames"]))
        : undefined;

    let currentFilter = { ...filter };

    for (let key in formValues) {
      if (
        formValues[key]!==0&&( !formValues[key] ||
          (Array.isArray(formValues[key]) && formValues[key].length === 0))
      ) {
        delete formValues[key];
        delete currentFilter[key];
      }
    }
    if (formValues.Date && formValues.Date[0] && formValues.Date[1]) {
      formValues["StartDate"] = formValues.Date[0].format("YYYY-MM-DDTHH:mm");
      formValues["EndDate"] = formValues.Date[1].format("YYYY-MM-DDTHH:mm");
      delete formValues.Date; // Date field'ını kaldır
    } else {
      delete currentFilter["StartDate"];
      delete currentFilter["EndDate"];
    }
    if(formValues["IsImportant"])
    {

      formValues["IsImportant"] = formValues["IsImport"]==="true"?true:false
    }

    const newFilter = { ...currentFilter, ...formValues };

    await dispatch(
      handleSetCalendarNotesFilter({
        filter: newFilter,
      })
    );
    mazakaForm.setSuccess(1000, () => {}, t("form.transactionSuccessful"));
    onFinish();
  };

  useEffect(() => {
    const startDate = filter.StartDate
    const endDate = filter.EndDate
    const date = []
    if(startDate)
    {
      date.push( dayjs(filter.StartDate))
    }
    if(endDate)
    {
      date.push( dayjs(filter.EndDate))
    }
 
    console.log("form filter",filter)
      form.setFieldsValue({
        Title: filter?.Title,
        Types: filter?.Types,
        Visibility: filter?.Visibility||filter?.Visibility==0?filter?.Visibility||filter?.Visibility:undefined,
        IsImportant: filter?.IsImportant? filter?.IsImportant?.toString():undefined,
     
        UserIds: filter?.UserIds,
        RelatedCustomerId: filter?.RelatedCustomerId,
        AttendeeUserIds: filter?.AttendeeUserIds,
        DepartmentIds: filter?.DepartmentIds,
        TagNames: filter?.TagNames,
        Date: date||[]
        
      });
    
  }, [filter]);

  
  return (
    <>
      <MazakaForm
        form={form}
        submitButtonVisible={false}
        onFinish={handleOnFinish}
      >
        <Row gutter={[20, 20]}>
          <MazakaInput
            label={t("calendar.title")}
            placeholder={t("calendar.title")}
            xs={24}
            name="Title"
           allowClear
          />
          <MazakaSelect
            label={t("calendar.noteType")}
            placeholder={t("calendar.noteType")}
            name="Types"
            xs={24}
            options={calendarNoteTypes}
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameTypes", obj?.label);
            }}
          />
          <MazakaSelect
            label={t("calendar.visibilities")}
            placeholder={t("calendar.visibilities")}
            name="Visibility"
            xs={24}
            options={visibilities}
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameVisibility", obj?.label);
            }}
          />
           <MazakaSelect
       name="IsImportant"
       label={t("calendar.priority")}
       placeholder={t("calendar.priority")}
       options={[
        {label:t("calendar.isImportant"),value:"true"},
        {label:t("calendar.standard"),value:"false"},
       ]}
       xs={24}
        onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameIsImportant", obj?.label);
            }}
      />
      
          {/* <MazakaSelect
            label={t("calendar.importanceStatus")}
            placeholder={t("calendar.importanceStatus")}
            name="IsImportant"
            xs={24}
            options={importantOptions}
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameIsImportant", obj?.label);
            }}
          /> */}

          <MazakaRangePicker xs={24}  name="Date"
          showTime
           label={t("calendar.rangeDateTime")}
           
          />
          <GeneralUsers
            placeholder={t("calendar.users")}
            label={t("calendar.users")}
            name="UserIds"
            xs={24}
            mode="multiple"
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameUserIds",JSON.parse(JSON.stringify( obj)));
            }}
          />
          <GeneralCustomerWithSearch
            label={t("calendar.relatedCustomer")}
            placeholder={t("calendar.relatedCustomer")}
            name="RelatedCustomerId"
            xs={24}
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameRelatedCustomerId", obj?.label);
            }}
          />
          <GeneralUsers
            placeholder={t("calendar.attendingUsers")}
            label={t("calendar.attendingUsers")}
            name="AttendeeUserIds"
            xs={24}
            mode="multiple"
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameAttendeeUserIds", JSON.parse(JSON.stringify( obj)));
            }}
          />
          <GeneralDepartments
            label={t("calendar.departments")}
            placeholder={t("calendar.departments")}
            name="DepartmentIds"
            xs={24}
            multiple={true}
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameDepartmentIds", JSON.parse(JSON.stringify( obj)));
            }}
          />

   

          <Col xs={24}>
            <MazakaButton
              htmlType="submit"
              processType={formActions.submitProcessType}
              status="save"
            >
              {t("users.filter.filterButton")}
            </MazakaButton>
          </Col>
          <Form.Item name={"customNameTypes"} className="!hidden"></Form.Item>
          <Form.Item name={"customNameVisibility"} className="!hidden"></Form.Item>
          <Form.Item name={"customNameIsImportant"} className="!hidden"></Form.Item>
          <Form.Item name={"customNameUserIds"} className="!hidden"></Form.Item>
          <Form.Item name={"customNameRelatedCustomerId"} className="!hidden"></Form.Item>
          <Form.Item name={"customNameAttendeeUserIds"} className="!hidden"></Form.Item>
          <Form.Item name={"customNameDepartmentIds"} className="!hidden"></Form.Item>
         
        </Row>
      </MazakaForm>
    </>
  );
};

export default DetailsFilter;
