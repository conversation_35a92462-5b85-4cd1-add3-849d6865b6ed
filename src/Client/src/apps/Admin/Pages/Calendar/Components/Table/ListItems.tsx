import { DeleteOutlined, FormOutlined } from "@ant-design/icons";
import { Col, Drawer, Modal, Table, Tooltip, Typography } from "antd";
import endPoints from "../../EndPoints";
import { useQueryClient } from "react-query";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { withColumnVisibility } from "@/apps/Common/WithColumnVisibility";
import { useGetCalendarNotess } from "../../ServerSideStates";
import { deleteCalendarNote } from "../../Services";
import { hanldleSetCallFilter } from "@/apps/Call/ClientSideStates";
import { useSearchParams } from "react-router-dom";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import dayjs from "dayjs";
import {
  determineCalendarNoteType,
  determineReminderChannel,
} from "@/helpers/Calendar";
import ArrayCell from "./ArrayCell";
import AddOrUpdateCalendarNotes from "../AddOrUpdate/AddOrUpdateCalenderNotes";
import { handleSetCalendarData } from "../../ClientSideStates";
import { excludeUnnesessaryKey } from "@/helpers/ExcludeUnNesessaryKey";

const ListItems = () => {
  
  const [searchParams, setSearchParams] = useSearchParams();
  const { t } = useTranslation();
  const { Text } = Typography;
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const { filter } = useSelector((state: RootState) => state.calander);
  const calendarNotes = useGetCalendarNotess(excludeUnnesessaryKey(filter));
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const TableWithColumnVisibility = withColumnVisibility("calendar-table-list")(
    Table
  );

  // URL parametresini dinleyip drawer'ı aç
  useEffect(() => {
    const calendarNoteId = searchParams.get("calendarNoteId");
    if (calendarNoteId) {
      setIsShowEditDrawer(true);
    }
  }, [searchParams]);
  const columns = [
    {
      title: t("calendar.title"),
      dataIndex: "Title",
      key: "Title",
      width: "10%",
      sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (value: string, record: any) => {
        return (
          <div>
            <span
              className={`w-[12px] !h-[12px] ${
                record?.IsImportant ? "!bg-[#e05b4a]" : "!bg-gray-400"
              }`}
            ></span>
            <ExpandableText
              title={t("calendar.title")}
              limit={40}
              text={value || ""}
            />
          </div>
        );
      },
    },
    {
      title: t("calendar.startDateTime"),
      dataIndex: "StartDate",
      key: "StartDate",
      width: "13%",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      render: (value: string) => {
        return <>{value && <Text className="!text-xs" >{dayjs(value).format("YYYY-MM-DD HH:mm")}</Text>}</>;
      },
    },
    {
      title: t("calendar.endDateTime"),
      dataIndex: "EndDate",
      key: "EndDate",
      width: "13%",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      render: (value: string) => {
        return <>{value && <Text className="!text-xs" >{dayjs(value).format("YYYY-MM-DD HH:mm")}</Text>}</>;
      },
    },

    {
      title: t("calendar.noteType"),
      dataIndex: "Type",
      key: "Type",
      width: "10%",
      // sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (value: number) => {
        return (
          <>
          
          <Text className="!text-xs">
                  {determineCalendarNoteType("value", value, t)}
                </Text>
          </>
        );
      },
    },

    {
      title: t("calendar.tag"),
      dataIndex: "Tags",
      key: "Tags",
      width: "10%",
     
      render: (value: any[]) => {
        return (
          <>
            <ArrayCell data={value} keyField={"TagName"} modalTitle="Tag" />
              
          </>
        );
      },
    },

    {
      title: t("calendar.reminderChannel"),
      dataIndex: "Reminders",
      key: "Reminders",
      width: "10%",
      // sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (values: any[]) => {
        return (
          <>
            {values?.map((item: any) => {
              return (
                <Text className="!text-xs">
                  {determineReminderChannel("value", item.Channel, t)}
                </Text>
              );
            })}
          </>
        );
      },
    },

    {
      title: t("calendar.relatedCustomer"),
      dataIndex: "RelatedCustomerName",
      key: "relatedCustomer",
      width: "10%",
      // sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">{value || ""}</Text>
          </>
        );
      },
    },

    {
      title: t("calendar.departments"),
      dataIndex: "Departments",
      key: "Department",
      width: "10%",
      // sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (value: any[]) => {
        return (
          <>
            <ArrayCell data={value} keyField={"DepartmentName"} modalTitle="Department" />
          </>
        );
      },
    },
    {
      title: t("calendar.attendingUsers"),
      dataIndex: "Attendees",
      key: "AttendeeUserIds",
      width: "10%",
      // sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (value: any[]) => {
        return (
          <>
            <ArrayCell data={value} keyField={"UserName"} modalTitle="Kullanici" />
          </>
        );
      },
    },

    {
      title: "",
      dataIndex: "edit",
      key: "action",
      width: "10%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !pr-6">
          <Tooltip title={t("users.list.edit")}>
            <FormOutlined
              className=" !text-[#0096d1] !text-sm"
              onClick={async (e) => {
                e.preventDefault();
                e.stopPropagation();
                await dispatch(handleSetCalendarData({data:record}))
                setIsShowEditDrawer(true);
              }}
            />
          </Tooltip>

          <Tooltip title={t("users.list.delete")}>
            <DeleteOutlined
              className=" !text-[#9da3af] !text-sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                confirm(record);
              }}
            />
          </Tooltip>
        </Col>
      ),
    },
  ];

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("sector.warning"),
      icon: null,
      content: t("sector.deleteModalDesc"),
      okText: t("sector.delete"),
      cancelText: t("sector.cancel"),
      onOk: async () => {
        try {
          await deleteCalendarNote(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getCalendarNotesListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetCallFilter({ filter: newFilter }));
  };
  return (
    <>
      <TableWithColumnVisibility
        columns={columns}
        dataSource={calendarNotes?.data?.Value?.Value}
        loading={calendarNotes.isLoading || calendarNotes.isFetching}
        onRow={(record) => {
          return {
            onClick: async (event) => {
             await dispatch(handleSetCalendarData({data:record}))
            
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: calendarNotes.data?.FilteredCount || 0,
          current: calendarNotes.data?.PageNumber,
          pageSize: calendarNotes.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        rowKey={"Id"}
      />
      <Drawer
        title={t("calendar.editCalendarNote")}
        open={isShowEditDrawer}
        width={"60%"}
        onClose={() => {
          searchParams.delete("calendarNoteId");
          setSearchParams(searchParams);
          setIsShowEditDrawer(false);
        }}
        destroyOnHidden
      >
        <AddOrUpdateCalendarNotes
          onFinish={() => {
            searchParams.delete("calendarNoteId");
            setSearchParams(searchParams);
            setIsShowEditDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default ListItems;
