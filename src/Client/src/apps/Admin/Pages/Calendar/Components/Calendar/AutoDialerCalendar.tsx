import { Calendar, Col, Row, CalendarProps } from "antd";
import { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { cloneElement, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import CalendarItemDetails from "./CalendarItemDetails";
import { useGetCalendarNotess } from "../../ServerSideStates";

const AutoDialerCalendar = () => {
  const [selectedDateTime, setSelectedDateTime] = useState<Dayjs | null>(null);
  const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId = params["customerId"] || searchParams.get("customerId");
  const calendar = useGetCalendarNotess(customerId?{
    PageNumber: 1,
    PageSize: 100,
    RelatedCustomerId: customerId,
  }:
{
   PageNumber: 1,
    PageSize: 100,
     RelatedCustomerPhone: searchParams.get("unSavedNumber"),
});

  const data = calendar.data?.Value?.Value || [];

  const cellRender: CalendarProps<Dayjs>["fullCellRender"] = (date, info) => {
    if (info.type !== "date") return info.originNode;

    const todays = data.filter((it: any) => {
      const s = dayjs(it.StartDate);
      const e = dayjs(it.EndDate);
      return date.isSame(s, "day") || date.isSame(e, "day");
    });

    if (todays.length === 0) return info.originNode;

    const isImportant = todays.some((it: any) => !!it.IsImportant);
    const anySameDay = todays.some((it: any) =>
      dayjs(it.StartDate).isSame(it.EndDate, "day")
    );

    let tw = "rounded-md w-full h-full flex items-center justify-center  ";
    if (isImportant) tw += " !bg-red-500 !text-white-500 !mx-1";
    else  tw += " !bg-blue-400 !text-white !mx-1";
   

    return cloneElement(info.originNode as React.ReactElement, {
      className: `${info.originNode.props.className ?? ""} ${tw}`,
    });
  };

  return (
    <Row gutter={[0, 20]}>
      <Col xs={24}>
        <Calendar
          fullscreen={false}
          onSelect={(d) => setSelectedDateTime(d)}
          fullCellRender={cellRender} // 🔑 gün numarası tek kalıyor
        />
      </Col>

      {selectedDateTime && (
        <Col xs={24} className="h-[420px] bg-[rgb(232,230,230)] overflow-auto">
          <CalendarItemDetails selectedDateTime={selectedDateTime} />
        </Col>
      )}
    </Row>
  );
};

export default AutoDialerCalendar;
