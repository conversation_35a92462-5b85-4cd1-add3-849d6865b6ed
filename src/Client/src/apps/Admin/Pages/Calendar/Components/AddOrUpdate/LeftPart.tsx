import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaCheckbox } from "@/apps/Common/MazakaCheckbox";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import { Checkbox, Col, Form, FormInstance, Row } from "antd";
import { FC, useState } from "react";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const LeftPart: FC<{
  formActions: any;

  form: FormInstance;
}> = ({ formActions,  form }) => {
  const { t } = useTranslation();
  const [isAllDay, setIsAllDay] = useState(false);
  const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(null);
  const { calendarData } = useSelector((state: RootState) => state.calander);
  return (
    <Row gutter={[20, 20]}>
      <MazakaInput
        label={t("calendar.title")}
        placeholder={t("calendar.title")}
        xs={24}
        name="Title"
        rules={[{ required: true, message: "" }]}
      />

<Col xs={24}xl={5} className="!flex items-center">
        <Form.Item valuePropName="checked"  label=" "  name="IsAllDay">
          <Checkbox
           onChange={(e) => {
            const status = e.target.checked;
            if (status) {
              const start = dayjs().startOf("day");
              const end = dayjs().endOf("day");
              setStartDate(start);
              form.setFieldsValue({
                StartDate: start,
                EndDate: end,
              });
            } else {
              setStartDate(null);
              form.resetFields(["StartDate", "EndDate"]);
            }
            setIsAllDay(status);
          }}
          >
            <span className="!text-xs"> {t("calendar.isAllDay")}</span>
          </Checkbox>
        </Form.Item>
      </Col>


      <>
      
        <MazakaDatePicker
          label={t("calendar.startDateTime")}
          xs={24}
          xl={9}
            rules={[{required:true,message:""}]}
          name="StartDate"
          showTime={true}
          onChange={(value:any) => {
            setStartDate(value);
            const endDate = form.getFieldValue("EndDate");
            if (endDate && value && dayjs(endDate).isBefore(value)) {
              form.setFieldsValue({ EndDate: null });
            }
          }}
        />
        <MazakaDatePicker
          label={t("calendar.endDateTime")}
          xs={24}
          md={9}
          name="EndDate"
          showTime={true}
          disablePastDates={!!startDate}
          disablePastTimes={!!startDate}
          minDate={startDate}
            rules={[{required:true,message:""}]}
        />
      </>

      <MazakaTextArea
        rows={8}
        label={t("calendar.description")}
        placeholder={t("calendar.description")}
        xs={24}
        name="Description"
        rules={[{required:true,message:""}]}
      />

<Col xs={24} md={12} >
          <Form.Item
          valuePropName="checked"
           name="IsRecurring"
          >
            <Checkbox>
             <span className="!text-xs" > {t("calendar.isRecurring")}</span>
            </Checkbox>
          </Form.Item>
      </Col>

  

      <Col xs={24}>
        <MazakaButton
          processType={formActions.submitProcessType}
          htmlType="submit"
          status="save"
        >
          {calendarData ? t("calendar.edit") : t("calendar.add")}
        </MazakaButton>
      </Col>
    </Row>
  );
};

export default LeftPart;
