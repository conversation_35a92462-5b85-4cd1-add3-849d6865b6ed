import { FC, useMemo, useState } from "react";
import {
  Drawer,
  <PERSON>,
  Col,
  Button,
  Tooltip,
  Typography,
  Pagination,
} from "antd";
import { useTranslation } from "react-i18next";
import { EditOutlined, PlusOutlined } from "@ant-design/icons";
import { useParams, useSearchParams } from "react-router-dom";
import AddOrUpdateCalendarNotes from "../AddOrUpdate/AddOrUpdateCalenderNotes";
import dayjs from "dayjs";
import CustomNoData from "@/apps/Common/CustomNoData";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import { useGetCalendarNotess } from "../../ServerSideStates";
import { determineCalendarNoteType } from "@/helpers/Calendar";
import { useDispatch } from "react-redux";
import { handleSetCalendarData } from "../../ClientSideStates";

// Mock event listesi


const CalendarItemDetails: FC<{ selectedDateTime: any }> = ({
  selectedDateTime,
}) => {
  const dispatch = useDispatch()
   
  const { t } = useTranslation();
 
  const [isShowEditEventDrawer, setIsShowEditEventDrawer] = useState(false);
  const [isShowAddEventDrawer, setIsShowAddEventDrawer] = useState(false);
  const { Text } = Typography;
  const selectedDateStr = dayjs(selectedDateTime).format("YYYY-MM-DD");
  const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId = params["customerId"] || searchParams.get("customerId");
 const calendar = useGetCalendarNotess(customerId?
  {
      PageNumber: 1,
      PageSize: 30,
      StartDate: dayjs(selectedDateTime).format("YYYY-MM-DD"),
      RelatedCustomerId: customerId,
    }:
{
    PageNumber: 1,
      PageSize: 30,
      StartDate: dayjs(selectedDateTime).format("YYYY-MM-DD"),
   RelatedCustomerPhone: searchParams.get("unSavedNumber"),
}
  
  );



  return (
    <>
      <Row className=" !p-2 " gutter={[0, 10]}>
        <Col xs={24} className="!flex justify-between">
          <Text className="!font-bold">
            {dayjs(selectedDateStr).format("YYYY-MM-DD")}
          </Text>
          <PlusOutlined
            className="!text-[#0096d1]"
            onClick={() => setIsShowAddEventDrawer(true)}
          />
        </Col>
        <Col xs={24}>
          {calendar?.data?.Value?.Value?.length <= 0 ? (
            <CustomNoData
              emptyClassName="!text-xs"
              description={t("calendar.noEventsFound")}
            />
          ) : (
            calendar?.data?.Value?.Value?.map((event:any) => (
              <Row
                gutter={[0, 10]}
                key={event.id}
                justify="space-between"
                align="middle"
                className="!border !p-3 !mb-2 !rounded !shadow-sm"
              >
                <Col>
                  <div className="!flex !gap-2 items-center">
                    <div className="!flex flex-col gap-1">
                      <Tooltip title={event.IsImportant?t("calendar.isImportant"):t("calendar.standard")} >

                      <span className={"!text-[8px] cursor-help"}>
                        {event.IsImportant ? "🔴" : "🔵"}
                      </span>
                      </Tooltip>
                      <Tooltip title={determineCalendarNoteType("value",event.Type,t)} >

                        <span className="!text-[8px] cursor-help">
                      {
                        (()=>{
                          switch(event?.Type)
                          {
                            case 0:
                              return <>
                              🟢
                              </>
                            case 1:
                              return <>
                              🔵
                              </>
                            case 2:
                              return <>
                              🟡
                              </>
                                case 3:
                              return <>
                              🔴
                              </>
                          }
                       
                          return(
                            
                            <>
                            </>
                          )
                        })()
                      }

                        </span>
                      </Tooltip>
                   
                    </div>
                    <div className="!flex flex-col gap-1">
                      <ExpandableText
                        title={""}
                        limit={24}
                        text={event?.Title}
                        textClassName={"!text-black !text-xs"}
                      />

                      <span className="!text-xs !text-gray-500">
                        {dayjs(event.StartDate).format("HH:mm")} -  {dayjs(event.EndDate).format("HH:mm")}
                      </span>
                    </div>
                  </div>
                </Col>
                <Col>
                  <Tooltip title={t("edit")}>
                    <EditOutlined
                      className="text-[#0096d1]"
                      onClick={async() => {
                         await dispatch(handleSetCalendarData({data:event}))
                         setIsShowEditEventDrawer(true)
                      }}
                    />
                  </Tooltip>
                </Col>
              </Row>
            ))
          )}
        </Col>
        <Col xs={24} className="!absolute bottom-4 right-4">
          <Pagination
            size="small"
            current={calendar.data?.Value?.PageNumber}
            total={calendar.data?.Value?.FilteredCount || 0}
            pageSize={calendar.data?.Value?.PageSize}
            showSizeChanger
            // onChange={handleChangePagination}
            showTotal={(total) => `${total}`}
          />
        </Col>
      </Row>

      {/* EDIT Drawer */}
      <Drawer
        title={t("calendar.editCalendarNote")}
        open={isShowEditEventDrawer}
        onClose={() => {
          searchParams.delete("calendarNoteId");
          setSearchParams(searchParams);
          setIsShowEditEventDrawer(false);
        }}
        width={"60%"}
      >
        <AddOrUpdateCalendarNotes
          onFinish={() => {
            searchParams.delete("calendarNoteId");
            setSearchParams(searchParams);
            setIsShowEditEventDrawer(false);
          }}
        />
      </Drawer>

      {/* ADD Drawer */}
      <Drawer
        title={t("calendar.addCalendarNote")}
        open={isShowAddEventDrawer}
        onClose={() => setIsShowAddEventDrawer(false)}
        width={"50%"}
      >
        <AddOrUpdateCalendarNotes
          onFinish={() => setIsShowAddEventDrawer(false)}
        />
      </Drawer>
    </>
  );
};

export default CalendarItemDetails;
