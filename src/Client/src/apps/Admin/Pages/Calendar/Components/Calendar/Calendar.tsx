import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import { useEffect, useRef, useState } from "react";
import { Drawer, message, Modal, Tooltip } from "antd";
import trLocale from "@fullcalendar/core/locales/tr";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import AddOrUpdateCalendarNotes from "../AddOrUpdate/AddOrUpdateCalenderNotes";
import { useTranslation } from "react-i18next";
import { DeleteOutlined } from "@ant-design/icons";
import { deleteCalendarNote, updateCalendarNoteWithPut } from "../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import "./Calendar.css";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import { useDispatch, useSelector } from "react-redux";
import { handleSetCalendarData, handleSetCalendarNotesFilter } from "../../ClientSideStates";
import { RootState } from "@/store/Reducers";
import { useGetCalendarNotess } from "../../ServerSideStates";
import { excludeUnnesessaryKey } from "@/helpers/ExcludeUnNesessaryKey";

const Calendar = () => {
  const { filter } = useSelector((state: RootState) => state.calander);
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const calendarRef = useRef<FullCalendar>(null);
  const [searchParams, setSearchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const calendars = useGetCalendarNotess(excludeUnnesessaryKey(filter));
  const [data, setData] = useState<any[]>([]);

  useEffect(() => {
    if (calendars.data) {
      let allEvents = calendars.data?.Value?.Value || [];
      let formattedData = allEvents.map((item: any) => ({
        id: item.Id,
        title: item.Title,
        start: item.StartDate,
        end: item.EndDate,
        backgroundColor: item.IsImportant ? "#d84141" : "#fffae4",
        borderColor: item.IsImportant ? "#ff4d4f" : "#1890ff",
        extendedProps: {
          isImportant: item.IsImportant,
          startTime: dayjs(item.StartDate).format("HH:mm"),
          endTime: dayjs(item.EndDate).format("HH:mm"),
          fulllData: { ...item },
        },
      }));

      setData(formattedData);
    }
  }, [calendars.data]);



  const confirmDelete = (record: any) => {
    Modal.confirm({
      title: t("sector.warning"),
      icon: null,
      content: t("sector.deleteModalDesc"),
      okText: t("sector.delete"),
      cancelText: t("sector.cancel"),
      onOk: async () => {
        try {
          await deleteCalendarNote(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getCalendarNotesListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  const handleEventDrop = async (info: any) => {
    const updatedStart = dayjs(info.event.start).format("YYYY-MM-DDTHH:mm");
    const updatedEnd = dayjs(info.event.end).format("YYYY-MM-DDTHH:mm");;
    const originalData = info.event.extendedProps.fulllData;

    const updatedData = {
      ...originalData,
      StartDate: updatedStart,
      EndDate: updatedEnd,
    };

    try {
      await updateCalendarNoteWithPut(updatedData);
      message.success(
        `${info.event.title} etkinliği ${dayjs(updatedStart).format(
          "YYYY-MM-DD HH:mm"
        )} tarihine taşındı`
      );
      queryClient.resetQueries({
                 queryKey: endPoints.getCalendarNotesListFilter,
                 exact: false,
               });
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };

  const handleEventResize = async (info: any) => {
    const updatedStart = dayjs(info.event.start).format("YYYY-MM-DDTHH:mm");
    const updatedEnd = dayjs(info.event.end).format("YYYY-MM-DDTHH:mm");
    const originalData = info.event.extendedProps.fulllData;
  
    const updatedData = {
      ...originalData,
      StartDate: updatedStart,
      EndDate: updatedEnd,
    };
  
    try {
      await updateCalendarNoteWithPut(updatedData);
      message.success(
        `${info.event.title} etkinliği ${dayjs(updatedStart).format("HH:mm")} - ${dayjs(updatedEnd).format("HH:mm")} olarak güncellendi`
      );
      queryClient.resetQueries({
        queryKey: endPoints.getCalendarNotesListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };
  

  const renderEventContent = (eventInfo: any) => {
    const { event } = eventInfo;
    const view = eventInfo.view.type;

    return (
      <div className="relative group text-xs p-1"
     
      >
        <ExpandableText
          title={""}
          limit={24}
          text={event.title || ""}
          textClassName={
            event.extendedProps.isImportant&&view!=="listWeek" ? "!text-white" : "!text-black"
          }
          eyIconColor={
            event.extendedProps.isImportant &&view!=="listWeek"? "!text-white" : "!text-black"
          }
        />

        <div
          className={
            event.extendedProps.isImportant ? "!text-white" : "!text-black"
          }
        >
          {event.extendedProps.startTime} - {event.extendedProps.endTime}
        </div>

        <div
          className="absolute top-[9px] right-[7px] opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          style={{ zIndex: 9999 }}
        >
          <Tooltip title={t("sector.delete")}>
            <DeleteOutlined
              className="!text-white  cursor-pointer !text-xs bg-black rounded-full p-1  flex items-center justify-center"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                confirmDelete({ Id: event.id });
              }}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  const handleDatesSet = (arg: any) => {
    const startDate = dayjs(arg.start).format("YYYY-MM-DDTHH:mm");
    const endDate = dayjs(arg.end).format("YYYY-MM-DDTHH:mm");
    const newFilter = { ...filter };
    newFilter["StartDate"] = startDate;
    newFilter["EndDate"] = endDate;
    dispatch(handleSetCalendarNotesFilter({ filter: newFilter }));
  };

  return (
    <div style={{ padding: "20px" }}>
      <FullCalendar
        ref={calendarRef}
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
        initialView="dayGridMonth"
        locale={trLocale}
        headerToolbar={{
          left: "prev,next today",
          center: "title",
          right: "dayGridMonth,timeGridWeek,timeGridDay,listWeek",
        }}
        events={data}
        editable={true}
        datesSet={handleDatesSet}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={true}
        eventClick={async(info) => {
          const fullData = info.event.extendedProps.fulllData;
          await dispatch(handleSetCalendarData({ data: fullData }));
          setIsShowEditDrawer(true)
        }}
        eventDrop={handleEventDrop}
        height="auto"
        eventResize={handleEventResize}
        eventDisplay="block"
        displayEventTime={true}
        eventTextColor="#fff"
        eventContent={renderEventContent}
        moreLinkClick="popover"
        moreLinkText={(num) => `+${num} daha`}
        buttonText={{
          today: "Bugün",
          month: "Ay",
          week: "Hafta",
          day: "Gün",
          list: "Liste",
        }}
        views={{
          timeGridWeek: {
            eventMaxStack: 1,
            moreLinkClick: "popover",
          },
          timeGridDay: {},
          dayGridMonth: {
            dayMaxEvents: 3,
            moreLinkClick: "popover",
          },
        }}
      />
      <Drawer
         title={t("calendar.editCalendarNote")}
        open={isShowEditDrawer}
        onClose={() => {
          searchParams.delete("calendarNoteId");
          setSearchParams(searchParams);
          setIsShowEditDrawer(false);
        }}
        width={"50%"}
      >
        <AddOrUpdateCalendarNotes
          onFinish={() => {
            searchParams.delete("calendarNoteId");
            setSearchParams(searchParams);
            setIsShowEditDrawer(false);
          }}
        />
      </Drawer>
    </div>
  );
};

export default Calendar;
