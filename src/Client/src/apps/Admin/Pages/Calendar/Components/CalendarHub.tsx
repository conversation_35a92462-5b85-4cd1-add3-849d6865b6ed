import { useEffect, useState } from "react";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import {
  HubConnectionBuilder,
  LogLevel,
  HttpTransportType,
} from "@microsoft/signalr";
import { notification, Button } from "antd";

const CalendarHub = () => {
  const [connection, setConnection] = useState<any>(null);

  const handleAramaYap = () => {
    console.log("📞 Arama yapıldı");
    notification.destroy(); // istersen kapatabilirsin
  };

  const handleTamamla = () => {
    console.log("✅ Tamamlandı");
    notification.destroy();
  };

  useEffect(() => {
    const newConnection = new HubConnectionBuilder()
      .withUrl(`${setBackEndUrl()}/hubs/calendarhub`, {
        accessTokenFactory: () => {
          return localStorage.getItem("access_token") || "";
        },
        skipNegotiation: true,
        transport: HttpTransportType.WebSockets,
        withCredentials: true,
      })
      .configureLogging(LogLevel.Information)
      .withAutomaticReconnect()
      .build();

    newConnection
      .start()
      .then(() => {
        console.log("SignalR bağlantısı kuruldu");
        setConnection(newConnection);
      })
      .catch((err) => console.error("SignalR bağlantı hatası:", err));

    return () => {
      if (connection) {
        connection.stop();
      }
    };
  }, []);

  useEffect(() => {
    if (connection) {
      connection.on("AutoCallStarted", async (data: any) => {
        console.log("auto call start------", data);

        notification.open({
          message: "Otomatik Arama Başladı",
          description: " Bir arama başlatıldı, ne yapmak istersiniz?",
          duration: 0, 
          style: {
            backgroundColor: "#1890ff", // AntD mavi
            border: "1px solid #096dd9",
            color: "black",
          },
          btn: (
            <>
              <Button type="default" onClick={handleAramaYap}>
                Arama Yap
              </Button>
              <Button type="primary" onClick={handleTamamla} style={{ marginLeft: 8 }}>
                Tamamla
              </Button>
            </>
          ),
        });
      });
    }

    return () => {
      if (connection) {
        connection.off("AutoCallStarted");
      }
    };
  }, [connection]);

  return <></>;
};

export default CalendarHub;
