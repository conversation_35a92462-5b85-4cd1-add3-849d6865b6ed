import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const InitialState: { filter: any,viewingMode:"calendar"|"list",calendarData:null|any } = {
  filter: {
    PageNumber: 1,
    PageSize: 100,
    
  },
  viewingMode:"calendar",
  calendarData:null,
};

const calendarNotesSlice = createSlice({
  name: "calendarNotesSlice",
  initialState: InitialState,
  reducers: {
    handleSetCalendarNotesFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    handleSetCalendarViewingModes: (state, action) => {
      let data = action.payload;
      state.viewingMode = data.mode;
    },
    handleSetCalendarData: (state, action) => {
      let data = action.payload;
      state.calendarData = data.data;
    },

    handleResetAllFieldsCalendarNotes: (state) => {
      Object.assign(state, InitialState);
    },
   handleResetFilterCalendarNotes: (state, action: PayloadAction<{ filter: any }>) => {
      const data = action.payload.filter;
      state.filter = {
        PageNumber: 1,
        PageSize: 100,
        StartDate: data?.StartDate,
        EndDate: data?.EndDate,
      };
    },
  },
});

export const {
  handleResetAllFieldsCalendarNotes,
  handleResetFilterCalendarNotes,
  handleSetCalendarNotesFilter,
  handleSetCalendarViewingModes,
  handleSetCalendarData
} = calendarNotesSlice.actions;
export default calendarNotesSlice;
