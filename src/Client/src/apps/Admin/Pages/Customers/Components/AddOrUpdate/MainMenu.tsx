import { Tabs, TabsProps, Typography } from "antd";
import General from "./General";
import Segmentations from "./Segmentations";
import AddressIndex from "./Address/AddressIndex";
import Service from "./Service";
import Call from "./Call";
import Ticket from "./Ticket/Ticket";
import Notes from "./Notes/Notes";
import ResponsibleUsers from "./ResponsibleUsers/ResponsibleUsers";
import { FC } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { useGetCustomerDetails } from "../../ServerSideStates";
import { useDispatch, useSelector } from "react-redux";
import { handleSetActiveDetailsMenu } from "../../ClientSideStates";
import { RootState } from "@/store/Reducers";
import { useTranslation } from "react-i18next";
import ChatIndex from "@/apps/Chat/ChatIndex";
import chatEndPoints from "@/apps/Chat/EndPoints"
import endPoints from "../../EndPoints"

import {
  hanldleSetSelectedUserListChatItem,
} from "@/apps/Chat/ClientSideStates";
import { useQueryClient } from "react-query";
import BlackListIndex from "../../../BlackList/BlackListIndex";

const MainMenu: FC<{ mode: "calling" | "customer" }> = ({ mode }) => {
  const queryClient = useQueryClient()
  const { t } = useTranslation();
  const { Text } = Typography;
  const { activeDetailsMenu } = useSelector(
    (state: RootState) => state.customer
  );
  const dispatch = useDispatch();
  const params = useParams();
  const customerId = params["customerId"];
  const [searchParams] = useSearchParams();
  const customerDetails = useGetCustomerDetails(
    customerId || searchParams.get("customerId") || ""
  );

  const items: TabsProps["items"] = [
    ...(mode === "calling"
      ? [
          {
            key: "7",
            label: (
              <Text>
                {t("ticket.list.ticket")}{" "}
                <span className="!text-red-500">(3)</span>
              </Text>
            ),
            children: <Ticket mode={mode} />,
          },

          {
            key: "9",
            label: t("customers.add.notes"),
            children: <Notes mode={mode} />,
          },
        ]
      : []),
    ...(!customerDetails?.data?.Value
      ? [
          {
            key: "1",
            label: t("customers.add.general"),
            children: <General />,
          },
        ]
      : [
          {
            key: "1",
            label: t("customers.add.general"),
            children: <General />,
          },
          {
            key: "22",
            label: t("customers.add.concat"),
            children: <ResponsibleUsers />,
          },
          {
            key: "2",
            label: t("customers.add.segmentations"),
            children: <Segmentations />,
          },
          {
            key: "3",
            label: t("customers.add.addresses"),
            children: <AddressIndex />,
          },
          {
            key: "4",
            label: t("customers.add.inform"),
            children: <Service />,
          },

          ...(mode === "customer"
            ? [
                {
                  key: "5",
                  label: t("customers.add.call"),
                  children: <Call mode={mode} />,
                },

                {
                  key: "7",
                  label: t("ticket.list.ticket"),
                  children: <Ticket mode={mode} />,
                },

                {
                  key: "9",
                  label: t("customers.add.notes"),
                  children: <Notes mode={mode} />,
                },
                {
                  key: "10",
                  label: t("customers.add.chats"),
                  children: <ChatIndex pageType={"customer"} />,
                },
                 {
                  key: "11",
                  label: t("customers.add.blackList"),
                  children: <BlackListIndex pageType={"customer"} />,
                },
              ]
            : []),
        ]),
  ];
  return (
    <div className="!py-2">
      <Tabs
        tabBarStyle={{ margin: "5px" }}
        defaultActiveKey="1"
        className="!position-relative"
        activeKey={activeDetailsMenu}
        items={items}
        onChange={(key) => {
          if (key === "10") {
            dispatch(hanldleSetSelectedUserListChatItem({ data: null }));
            queryClient.resetQueries({
              queryKey: chatEndPoints.getUserChatListFilter,
              exact: false,
            });
          }
          else if (key==="9"){
            queryClient.resetQueries({
              queryKey: endPoints.getCustomerCallNoteListFilter,
              exact: false,
            });
          }
          else if (key==="5"){
            queryClient.resetQueries({
              queryKey: endPoints.getCustomerCallListFilter,
              exact: false,
            });
          }
          dispatch(handleSetActiveDetailsMenu({ activeMenu: key }));
        }}
      />
    </div>
  );
};

export default MainMenu;
