import { Col, Row } from "antd";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import GeneralPhoneNumber2 from "@/apps/Common/GeneralPhoneNumber2";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { useEffect, useState } from "react";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import GeneralCustomerSource from "@/apps/Common/GeneralCustomerSource";
import { useSharedForm } from "@/hooks/useSharedForm";
import { useParams, useSearchParams } from "react-router-dom";
import GeneralSector from "@/apps/Common/GeneralSector";
import GeneralProfession from "@/apps/Common/GeneralProfession";
import GeneralLanguage from "@/apps/Common/GeneralLanguage";
import GeneralCustomerWithSearch from "@/apps/Common/GeneralCustomerWIthSearch";
import { useTranslation } from "react-i18next";
import GeneralCustomerClassification from "@/apps/Common/GeneralCustomerClassification";
import { useGetCustomerDetailsByType } from "../../../TempCustomer/ServerSideStates";
import GeneralNotificationWays from "@/apps/Common/GeneralNotificationWays";
import { MazakaSwitch } from "@/apps/Common/MazakaSwitch";


const General = () => {
  const [selectType, setSelectType] = useState<number | null>(1);
  const { t } = useTranslation();
  const form = useSharedForm();

  const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId = params["customerId"] || searchParams.get("customerId");
  const customerDetails = useGetCustomerDetailsByType(
    customerId,
    searchParams.get("type") === "tempCustomer" ? "tempCustomer" : "customer"
  );
  useEffect(() => {
    if (customerDetails?.data?.Value) {
      setSelectType(customerDetails?.data?.Value?.Type);
    }
  }, [customerDetails.data]);

  return (
    <>
      <Row>
        <Col xs={24}>
          <Row gutter={[20, 20]}>
            <MazakaSelect
              label={t("customers.list.type")}
              placeholder={t("customers.list.type")}
              name="Type"
              xs={24}
              md={12}
              lg={8}
              options={[
                { label: t("customers.add.individual"), value: 1 },
                { label: t("customers.add.corporate"), value: 2 },
              ]}
              onChange={(value: number) => {
                setSelectType(value);
              }}
              rules={[{ required: true, message: "" }]}
            />

            {selectType === 1 && (
              <>
                <MazakaInput
                  label={t("customers.add.name")}
                  placeholder={t("customers.add.name")}
                  name="Name"
                  xs={24}
                  md={12}
                  xl={8}
                  rules={[{ required: true, message: "" }]}
                />
                <MazakaInput
                  label={t("customers.add.surName")}
                  placeholder={t("customers.add.surName")}
                  name={"Surname"}
                  xs={24}
                  md={12}
                  xl={8}
                  rules={[{ required: true, message: "" }]}
                />
              </>
            )}
            <GeneralSector
              label={t("customers.list.sector")}
              placeholder={t("customers.list.sector")}
              name="SectorId"
              xs={24}
              md={12}
              xl={8}
              value={customerDetails?.data?.Value?.SectorId}
            />
            <GeneralProfession
              label={t("customers.add.profession")}
              placeholder={t("customers.add.profession")}
              name="ProfessionId"
              xs={24}
              md={12}
              xl={8}
              value={customerDetails?.data?.Value?.ProfessionId}
            />
            {selectType === 2 && (
              <>
                <MazakaInput
                  label={t("customers.add.companyName")}
                  placeholder={t("customers.add.companyName")}
                  name="Name"
                  xs={24}
                  md={12}
                  xl={8}
                  rules={[{ required: true, message: "" }]}
                />

                <GeneralCustomerWithSearch
                  label={t("customers.add.topCompany")}
                  placeholder="Üst Firma"
                  name="TopCustomerId"
                  xs={24}
                  md={12}
                  xl={8}
                />
              </>
            )}
            <MazakaInput
              label={t("customers.list.email")}
              placeholder={t("customers.list.email")}
              name="Email"
              xs={24}
              md={12}
              lg={8}
              rules={[
              
                { type: "email", message: t("error.validationEmailType") },
              ]}
            />

            <GeneralPhoneNumber2
              label={t("customers.list.phone")}
              placeholder={t("customers.list.phone")}
              name="Phone"
              form={form}
              xs={24}
              md={12}
              xl={8}
              rules={[{ required: true, message: "" }]}
              pageType="customer"
            />

            <MazakaSelect
              label={t("customers.add.customerKind")}
              placeholder={t("customers.add.customerKind")}
              name="Kind"
              xs={24}
              md={12}
              lg={8}
              options={[
                { label: t("customers.add.customer"), value: 1 },
                { label: t("customers.add.potentialCustomer"), value: 2 },
                { label: t("customers.add.renew"), value: 3 },
              ]}
            />
            <GeneralCustomerSource
              label={t("customers.add.customerSource")}
              placeholder={t("customers.add.customerSource")}
              name="CustomerSourceId"
              xs={24}
              md={12}
              xl={8}
            />

            <MazakaSelect
              label={t("customers.add.customerStatus")}
              placeholder={t("customers.add.customerStatus")}
              name={"Status"}
              xs={24}
              md={12}
              lg={8}
              options={[
                { label: t("customers.add.active"), value: 1 },
                { label: t("customers.add.passive"), value: 2 },
                { label: t("customers.add.suspended"), value: 3 },
              ]}
            />

            <MazakaInput
              label={t("customers.add.taxOffice")}
              placeholder={t("customers.add.taxOffice")}
              name="TaxOffice"
              xs={24}
              md={12}
              xl={8}
            />
            {selectType === 2 ? (
              <MazakaInput
                label={t("customers.add.taxNumber")}
                placeholder={t("customers.add.taxNumber")}
                name="TaxNumber"
                xs={24}
                md={12}
                xl={8}
                type="number"
              />
            ) : (
              <MazakaInput
                label={t("customers.add.identificationNumber")}
                placeholder={t("customers.add.identificationNumber")}
                name={"IdentificationNumber"}
                xs={24}
                md={12}
                xl={8}
                type="number"
              />
            )}
            {searchParams.get("type") === "tempCustomer" && (
              <GeneralCustomerClassification
                label={t("customers.list.classification")}
                placeholder={t("customers.list.classification")}
                className=""
                mode="multiple"
                name="ClassificationIds"
                tooltip={
                  t("customers.add.classificationDesc") +
                  " " +
                  t("customers.add.classificationDescExample")
                }
                xs={24}
                md={12}
                lg={8}
              />
            )}

            <GeneralLanguage
              label={t("customers.add.mainLanguage")}
              placeholder={t("customers.add.mainLanguage")}
              name="MainLanguage"
              xs={24}
              md={12}
              xl={  searchParams.get("type") !== "tempCustomer"?6:8}
            />
            <GeneralLanguage
              label={t("customers.add.availableLanguages")}
              placeholder={t("customers.add.availableLanguages")}
              name="AvailableLanguage"
              xs={24}
              md={12}
              xl={  searchParams.get("type") !== "tempCustomer"?6:8}
              mode="multiple"
            />
            <GeneralNotificationWays
              label={t("notificationWay.notificationMethod")}
              placeholder={t("notificationWay.notificationMethod")}
              name="NotificationWayId"
              xs={24}
              md={12}
              xl={  searchParams.get("type") !== "tempCustomer"?6:8}

            />
            {
              searchParams.get("type") !== "tempCustomer"&&
              
            <MazakaSwitch
             label={t("customers.add.hideInformation")}
             xs={24}
             md={12}
             xl={6}
             name="HideInformation"
            
            />
              
            }
            {searchParams.get("type") === "tempCustomer" && (
              <>
                <MazakaInput
                  name={"Country"}
                  xs={24}
                  md={8}
                  label={t("customers.add.country")}
                  placeholder={t("customers.add.country")}
                />
                <MazakaInput
                  name={"State"}
                  xs={24}
                  md={8}
                  label={t("customers.add.state")}
                  placeholder={t("customers.add.state")}
                />
                <MazakaInput
                  name={"City"}
                  xs={24}
                  md={8}
                  label={t("customers.add.city")}
                  placeholder={t("customers.add.city")}
                />
                 <MazakaInput
                  name={"Address"}
                  xs={24}
                  md={8}
                  label="Açık Adres"
                  placeholder="Açık Adres"/>
              </>
            )}

            <MazakaTextArea
              xs={24}
              label={t("customers.add.description")}
              placeholder={t("customers.add.description")}
              name="Description"
            />
          </Row>
        </Col>
      </Row>
    </>
  );
};

export default General;
