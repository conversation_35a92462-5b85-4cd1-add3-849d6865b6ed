import PageTitle from "@/apps/Common/PageTitle";
import { Col, Divider, Row,  } from "antd";
import { ArrowLeftOutlined, CloseOutlined } from "@ant-design/icons";
import MainMenu from "./MainMenu";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { FC } from "react";
import WrapperGeneralForm from "./WrapperGeneralForm";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import General from "./General";
import { useTranslation } from "react-i18next";
import {
  hanldleSetTicketFilter,
  hanldleSetTicketPageType,
} from "../../../Ticket/ClientSideStates";
import { useGetCustomerDetailsByType, } from "../../../TempCustomer/ServerSideStates";
import { handleSetActiveDetailsMenu } from "../../ClientSideStates";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints"
import ConvertToCustomer from "./ConvertToCustomer";
import { commonRoutePrefix } from "@/routes/Prefix";



const AddOrUpdateCustomerIndex: FC<{ type: "drawer" | "url",onFinish?:any }> = ({ type,onFinish }) => {

 
  const navigate = useNavigate();
  const {activeSocketList} = useSelector((state:RootState)=>state.autoDialer)
  const { activeDetailsMenu } = useSelector(
    (state: RootState) => state.customer
  );
  const { filter: ticketFilter } = useSelector(
    (state: RootState) => state.ticket
  );
  const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId = params["customerId"] || searchParams.get("customerId");

  // const customerDetails =
  //   searchParams.get("type") === "tempCustomer"
  //     ? useGetTempCustomerDetails(customerId)
  //     : useGetCustomerDetails(customerId);
    const customerDetails =useGetCustomerDetailsByType(customerId, searchParams.get("type") === "tempCustomer"?"tempCustomer":"customer")
  const { t } = useTranslation();
  const dispatch = useDispatch();
 const queryClient = useQueryClient()
  return (
    <>
      <Col xs={24}>
        <WrapperGeneralForm>
          <Row className="!px-2 !w-full">
            <Col xs={24} className="">
              <div className="!flex gap-2 items-center !p-1">
                {type === "url" ? (
                  <ArrowLeftOutlined
                    onClick={() => {
                      const currentFilter = { ...ticketFilter };
                      delete currentFilter["CustomerId"];
                      dispatch(
                        hanldleSetTicketFilter({ filter: currentFilter })
                      );
                      dispatch(hanldleSetTicketPageType({ type: "tickets" }));
                      dispatch(handleSetActiveDetailsMenu({ key: "1" }));
                      if(searchParams.get("type")==="tempCustomer")
                      {
                       
                        navigate(`${commonRoutePrefix}/import-data`);
                      }
                      else{

                        navigate(`${commonRoutePrefix}/customers`);
                      }
                      queryClient.resetQueries({
                        queryKey: endPoints.getCustomerListFilter,
                        exact: false,
                      });
                    }}
                    className="!text-xl !text-gray-400"
                  />
                ):<>
                {
                  onFinish&&
                  <CloseOutlined onClick={()=>{
                    onFinish()
                  }} className="!text-[#8c8c8c] cursor-pointer" />
                }
                </>}

                
                <PageTitle
                  title={
                    customerId
                      ? t("customers.add.editCustomer")
                      : t("customers.add.addCustomer")
                  }
                  isSubTitle
                />
                

                {activeDetailsMenu !== "22" &&
                activeDetailsMenu !== "3" &&
                activeDetailsMenu !== "5" &&
                activeDetailsMenu !== "6" &&
                activeDetailsMenu !== "9"&&
                activeDetailsMenu !== "10" ? (
                  <>
                  
                    {activeDetailsMenu !== "1" && customerId && (
                      <>
                        {`(${customerDetails?.data?.Value?.Name || ""} ${
                          customerDetails?.data?.Value?.Surname || ""
                        })`}
                        
                      </>
                    )}
                      <MazakaButton htmlType="submit">
                      {t("customers.add.save")}
                    </MazakaButton>
                  </>
                ) : (
                  <>
                    {customerId && (
                      <>
                        {`(${customerDetails?.data?.Value?.Name || ""} ${
                          customerDetails?.data?.Value?.Surname || ""
                        })`}
                       
                      </>
                    )}
                  </>
                )}

                {
                  activeSocketList?.length>0&&searchParams.get("type")==="tempCustomer"&&searchParams.get("customerId")&&
                  <ConvertToCustomer
                  showingType="button"
                  pageType="calling"
                  record={{Id:searchParams.get("customerId")}}
                  />
                }
               
              </div>
            </Col>
            <Col xs={24}>
              <Divider className="!m-0" />
            </Col>
            <Col xs={24}>
              {searchParams.get("type") === "tempCustomer" ? (
                <>
                  <General />
                </>
              ) : (
                <>
                  <MainMenu mode="customer" />
                </>
              )}
            </Col>
          </Row>
        </WrapperGeneralForm>
      </Col>
 
    </>
  );
};

export default AddOrUpdateCustomerIndex;
