import { MazakaForm } from "@/apps/Common/MazakaForm";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Form } from "antd";
import { FC, useEffect } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FormContext } from "@/hooks/useSharedForm";

import { createCustomer, updateCustomerWithPut } from "../../Services";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetCustomerDetailsByType } from "../../../TempCustomer/ServerSideStates";
import { updateTempCustomerWithPut } from "../../../TempCustomer/Services";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { commonRoutePrefix } from "@/routes/Prefix";
import chatEndPoints from "@/apps/Chat/EndPoints";
import { checkCustomer } from "@/apps/Chat/Services";
import { splitPhoneNumber } from "@/helpers/SplitPhoneNumber";
import { getBlackListListFilter, updateBlackListWithPut } from "../../../BlackList/Services";

const WrapperGeneralForm: FC<{ children: any }> = ({ children }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { activeDetailsMenu } = useSelector(
    (state: RootState) => state.customer
  );
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);

  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const customerId =  searchParams.get("customerId")||params["customerId"]||"" ;

  const customerDetails = useGetCustomerDetailsByType(
    customerId,
    searchParams.get("type") === "tempCustomer" ? "tempCustomer" : "customer"
  );

  useEffect(() => {
    if (customerDetails?.data?.Value) {
      const data = { ...customerDetails.data?.Value };
      data["ClassificationIds"] = data["Classifications"]?.map(
        (item: any) => item.Id
      );
      data["AdvisorIds"] = data["AdvisorIds"]
      data["AvailableLanguage"] = data["AvailableLanguage"]
        ? JSON.parse(data["AvailableLanguage"])
        : [];

      if (data?.PhonePrefix && data?.Phone) {
        const prefix = data?.PhonePrefix?.replace("+", "");
        data["Phone"] = prefix + data?.Phone;
      }

      data["MailBcc"] = data["MailBcc"] ? JSON.parse(data["MailBcc"]) : [];
      form.setFieldsValue({
        ...data,
      });
    }
  }, [customerDetails?.data]);

  useEffect(() => {
    if (searchParams.get("unSavedNumber") && !searchParams.get("customerId")) {
      form.resetFields();
      form.setFieldValue("Phone", `90${searchParams.get("unSavedNumber")}`);
    }
  }, [searchParams]);

  const hangleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();

    formValues["AvailableLanguage"] = formValues["AvailableLanguage"]
      ? JSON.stringify(formValues["AvailableLanguage"])
      : "";
    formValues["Surname"] = formValues["Surname"] || " ";
    formValues["MailBcc"] = formValues["MailBcc"]
      ? JSON.stringify(formValues["MailBcc"])
      : "";

    if (formValues["Phone"]?.includes("+")) {
      let split_number = formValues["Phone"].split("+");
      formValues["PhonePrefix"] = split_number[0];
      formValues["Phone"] = formValues["Phone"]
        ?.replace("+", "")
        ?.replace(split_number[0], "");
    } 
    else if(searchParams.get("unSavedNumber"))
    {
     
      formValues["PhonePrefix"] = searchParams.get("phonePrefix");
      formValues["Phone"] = searchParams.get("unSavedNumber");
    }
    else if(searchParams.get("chatCustomerPhone"))
      {
        const data = splitPhoneNumber(searchParams.get("chatCustomerPhone")||"");
        formValues["PhonePrefix"] = data.dialCode;
        formValues["Phone"] = data.nationalNumber;
      }
    else {
      formValues["PhonePrefix"] = customerDetails.data?.Value?.PhonePrefix;
      formValues["Phone"] = customerDetails.data?.Value?.Phone;
    }
   

    try {
      if (customerId) {
        const currentData = customerDetails?.data?.Value;
        if (searchParams.get("type") !== "tempCustomer") {
          delete currentData["Classifications"];
        }
        searchParams.get("type") === "tempCustomer"
          ? await updateTempCustomerWithPut({ ...currentData, ...formValues })
          : await updateCustomerWithPut({ ...currentData, ...formValues });
      } else {
        const response = await createCustomer(formValues);
        if (searchParams.get("callId")) {
          const callId = searchParams.get("callId");

          setSearchParams({
            callId: callId || "", 
            customerId: response?.Value || "", 
          });
        }
        else if (searchParams.get("chatCustomerName")){
          const newParams = new URLSearchParams(searchParams.toString());
          newParams.set("customerId", response?.Value || "");
          
          setSearchParams(newParams);
          
          await checkCustomer(searchParams.get("ticketChatId")||"");
          queryClient.resetQueries({
            queryKey: chatEndPoints.getUserChatListFilter,
            exact: false,
          });
          queryClient.resetQueries({
            queryKey: chatEndPoints.getChatMessageDetails,
            exact: false,
          });
        }
        else if (searchParams.get("addOrUpdateTicket")==="true")
        {
          const newParams = new URLSearchParams(searchParams.toString());
          newParams.set("customerId", response?.Value || "");
          setSearchParams(newParams);
        }
        else {
          navigate(`${commonRoutePrefix}/edit-customer/${response?.Value}`);
          
        }
      }
   
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      queryClient.resetQueries({
        queryKey: endPoints.getCustomerListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, mazakaForm, false, t);
    }
  };

  useEffect(()=>{
    if(searchParams.get("chatCustomerName")){
      form.setFieldValue("Name",searchParams.get("chatCustomerName"))
      form.setFieldValue("Phone",searchParams.get("chatCustomerPhone"))
    }
  },[searchParams])

  return (
    <>
      {activeDetailsMenu === "22" ||
      activeDetailsMenu == "3" ||
      activeDetailsMenu == "5" ||
      activeDetailsMenu == "6" ||
      activeDetailsMenu == "9"||
      activeDetailsMenu == "10" ? (
        <>
          <FormContext.Provider value={form}>{children}</FormContext.Provider>
        </>
      ) : (
        <>
          <FormContext.Provider value={form}>
            <MazakaForm
              initialValues={{ Status: 1, MainLanguage: "TR",HideInformation:false }}
              onFinish={hangleOnFinish}
              form={form}
              submitButtonVisible={false}
              {...formActions}
            >
              {children}
            </MazakaForm>
          </FormContext.Provider>
        </>
      )}
    </>
  );
};

export default WrapperGeneralForm;
