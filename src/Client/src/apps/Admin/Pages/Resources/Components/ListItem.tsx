import {
  DeleteOutlined,
  FormOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { Col, Drawer,  Modal, Table, Tag, Tooltip, Typography } from "antd";

import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";

import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import {  useGetResources,  } from "../ServerSideStates";
import {  useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { deleteResource } from "../Services";
import { hanldleSetResourceFilter } from "../ClientSideStates";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { withColumnVisibility } from "@/apps/Common/WithColumnVisibility";
import AddOrUpdateResource from "./AddOrUpdateResource";



const ListItems = () => {
  const {Text} = Typography
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const {filter} = useSelector((state:RootState)=>state.resource)
  const Resources= useGetResources(filter);
  const queryClient = useQueryClient();
  const dispatch = useDispatch()
  const {t} = useTranslation()
 const TableWithColumnVisibility = withColumnVisibility("resources")(Table);
  const columns = [
    {
      title: t("resource.key"),
      dataIndex: "Key",
      key: "key",
      width:"25%",
      sorter: (a: any, b: any) => a?.Key.localeCompare(b?.Key),
      render:(value:string)=>{
        return(
          <>
          <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },
    {
      title: t("resource.value"),
      dataIndex: "Value",
      key: "value",
      width:"25%%",
      sorter: (a: any, b: any) => a?.Value.localeCompare(b?.Value),
      render:(value:string)=>{
        return(
          <>
          <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },
    {
      title: t("resource.language"),
      dataIndex: "LanguageCode",
      key: "LanguageCode",
      width:"25%%",
      sorter: (a: any, b: any) => a?.Value.localeCompare(b?.Value),
      render:(value:string)=>{
        return(
          <>
          <Tag color={value==="tr"?"green":"blue"} >{value==="tr"?t("resource.tr"):t("resource.en")}</Tag>
          </>
        )
      },
    },
  
    {
      title: "",
      dataIndex: "edit",
      key: "action",
      width:"25%%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !pr-6">
        <Tooltip title={t("users.list.edit")}>
          <FormOutlined
            className=" !text-[#0096d1] !text-sm"
            onClick={async(e) => {
              e.preventDefault();
              e.stopPropagation();
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
             
            }}
          />
        </Tooltip>
        {
          record?.IsFromDatabase&&
        <Tooltip title={t("resource.default")}>
          <SyncOutlined
            className=" !text-[#9da3af] !text-sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              confirm(record);
            }}
          />
        </Tooltip>
        }

      </Col>
      ),
    },
  ];

 

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("resource.warning"),
      icon: null,
      content: t("resource.deleteModalDesc"),
      okText:  t("resource.delete"),
      cancelText:  t("resource.cancel"),
      onOk: async () => {
        try {
          await deleteResource(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getResourceListFilter,
            exact: false,
          });
        } catch (error: any) {
         showErrorCatching(error,null,false,t)
        }
      },
    });
  };
const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetResourceFilter({ filter: newFilter }));
  };
  return (
    <>
      <TableWithColumnVisibility
        columns={columns}
        dataSource={Resources?.data?.Value?.Value||[]}
        loading={Resources.isLoading||Resources.isFetching}
        onRow={(record:any) => {
          return {
            onClick: async(event:any) => {
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            onChange: handleChangePagination,
            total: Resources.data?.Value?.FilteredCount || 0,
            current: Resources.data?.Value?.PageNumber,
            pageSize: Resources.data?.Value?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e:any) => `${e}`,
          }}
        rowKey={"Id"}
      />
      <Drawer
        title={t("resource.editResource")}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateResource
          selectedRecord={selectedRecord}
          onFinish={() => setIsShowEditDrawer(false)}
        />
      </Drawer>
      
    </>
  );
};

export default ListItems;
