import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import endPoints from "../EndPoints";
import { useParams, useSearchParams } from "react-router-dom";
import { StopOutlined } from "@ant-design/icons";
import { Button, Modal } from "antd";
import { checkOnBlackLis, createBlackList } from "../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useGetCustomerDetailsByType } from "../../TempCustomer/ServerSideStates";
import { useQueryClient } from "react-query";


const BlockedCustomerButton = () => {
  const [isOnBlackList,setIsOnBlackList] = useState(false)
  const queryClient = useQueryClient();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId = params["customerId"] || searchParams.get("customerId");
  const unSavedPhone = searchParams.get("unSavedNumber");
  const customerDetails = useGetCustomerDetailsByType(
    customerId,
    searchParams.get("type") === "tempCustomer" ? "tempCustomer" : "customer"
  );

  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const handleBlockedCustomer = async () => {
    setIsLoading(true);
    try {
      let data = {};
      if (customerId) {
        data = {
          CustomerId: customerId,
          ContactType: 2,
          ContactValue: customerDetails?.data?.Value?.Phone,
          BlockedChannels: 7,
        };
      } else {
        data = {
          ContactType: 2,
          ContactValue: unSavedPhone,
          BlockedChannels: 7,
        };
      }
      await createBlackList(data);
      openNotificationWithIcon("success", t("form.transactionSuccessful"));

      queryClient.resetQueries({
        queryKey: endPoints.getBlackListListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, null, false, t);
    } finally {
      setIsLoading(false);
    }
  };

  const confirm = () => {
    Modal.confirm({
      title: t("blackList.warning"),
      icon: null,
      content: t("blackList.addCustomerBlackListDesc"),
      okText: t("blackList.ok"),
      cancelText: t("blackList.cancel"),
      onOk: async () => {
        try {
          handleBlockedCustomer();
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  const handleCheckOnBlackList = async()=>{
    try {
      let data = {
        ContactType:2,
        ContactValue:customerId?customerDetails?.data?.Value?.Phone:unSavedPhone
      }
      const response = await checkOnBlackLis(data)
      if(response?.Value?.length>0)
      {
        setIsOnBlackList(true)
      }
      else{
        setIsOnBlackList(false)
      }
    } catch (error) {
      showErrorCatching(error,null,false,t)
    }
  }

  useEffect(()=>{
    handleCheckOnBlackList ()
  },[customerId,unSavedPhone])



  return (
    <>
      <Button
        loading={isLoading}
        disabled={isLoading||isOnBlackList}
        className={`${isLoading||isOnBlackList?"!bg-red-300":"!bg-[#e05b4a]"} !text-xs !text-white`}
        icon={<StopOutlined />}
        onClick={confirm}
      >
        {t("blackList.blockCustomer")}
      </Button>
    </>
  );
};

export default BlockedCustomerButton;
