import {
  DeleteOutlined,
  FormOutlined,
} from "@ant-design/icons";
import { Col, Drawer,  Modal, Table, Tooltip, Typography } from "antd";

import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";

import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import {  useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { withColumnVisibility } from "@/apps/Common/WithColumnVisibility";
import { useGetBlackLists } from "../ServerSideStates";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import dayjs  from 'dayjs';
import { deleteBlackList } from "../Services";
import { hanldleSetBlackListFilter } from "../ClientSideStates";
import AddOrUpdateBlackList from "./AddOrUpdateBlackList";
import { determineBlackListChannel, determineBlackListContactType } from "@/helpers/BlackList";



const ListItems = () => {
  const {Text} = Typography
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const {filter} = useSelector((state:RootState)=>state.blackList)
  const blackLists= useGetBlackLists(filter);
  const queryClient = useQueryClient();
  const dispatch = useDispatch()
  const {t} = useTranslation()
 const TableWithColumnVisibility = withColumnVisibility("blackLists")(Table);
  const columns = [
    {
      title: t("blackList.contactType"),
      dataIndex: "ContactType",
      key: "contactType",
      width:"15%",
      sorter: (a: any, b: any) => a?.ContactType-b?.ContactType,
      render:(value:number)=>{
        return(
          <>
          <Text className="!text-xs" >{determineBlackListContactType("value",value,t)}</Text>
          </>
        )
      },
    },
       {
      title: t("blackList.contactValue"),
      dataIndex: "ContactValue",
      key: "ContactValue",
   width:"15%",
      sorter: (a: any, b: any) => a?.ContactValue.localeCompare(b?.ContactValue),
      render:(value:string)=>{
        return(
          <>
          <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },

       {
      title: t("blackList.blockedChannels"),
      dataIndex: "BlockedChannels",
      key: "BlockedChannels",
   width:"15%",
      sorter: (a: any, b: any) => a?.BlockedChannels.localeCompare(b?.BlockedChannels),
      render:(value:number)=>{
        return(
          <>
          <Text className="!text-xs" >{determineBlackListChannel("value",value,t)}</Text>
          </>
        )
      },
    },
         {
      title: t("blackList.customer"),
      dataIndex: "CustomerName",
      key: "CustomerName",
   width:"15%",
      sorter: (a: any, b: any) => a?.CustomerName.localeCompare(b?.CustomerName),
      render:(value:string)=>{
        return(
          <>
          <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },
         {
      title: t("blackList.expiryDate"),
      dataIndex: "ExpiryDate",
      key: "expiryDate",
   width:"15%",
      // sorter: (a: any, b: any) => a?.ExpiryDate.localeCompare(b?.ExpiryDate),
      render:(value:string)=>{
        return(
          <>
            {
              value&&
               <Text className="!text-xs" >{dayjs(value).format("YYYY-MM-DD")}</Text>
            }
          </>
        )
      },
    },
    
       {
      title: t("blackList.reason"),
      dataIndex: "Reason",
      key: "reason",
 
      sorter: (a: any, b: any) => a?.reason.localeCompare(b?.reason),
      render:(value:string)=>{
        return(
          <>
            <ExpandableText
                        title={""}
                        limit={24}
                        text={value||""}
                        textClassName={"!text-black !text-xs"}
                      />
          </>
        )
      },
    },
    
    
  
    {
      title: "",
      dataIndex: "edit",
      key: "action",
      width:"70%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
        <Tooltip title={t("users.list.edit")}>
          <FormOutlined
            className=" !text-[#0096d1] !text-sm"
            onClick={async(e) => {
              e.preventDefault();
              e.stopPropagation();
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
             
            }}
          />
        </Tooltip>

        <Tooltip title={t("users.list.delete")}>
          <DeleteOutlined
            className=" !text-[#9da3af] !text-sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              confirm(record);
            }}
          />
        </Tooltip>
      </Col>
      ),
    },
  ];

 

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("blackList.warning"),
      icon: null,
      content: t("blackList.deleteModalDesc"),
      okText:  t("blackList.delete"),
      cancelText:  t("blackList.cancel"),
      onOk: async () => {
        try {
          await deleteBlackList(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getBlackListListFilter,
            exact: false,
          });
        } catch (error: any) {
         showErrorCatching(error,null,false,t)
        }
      },
    });
  };
const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetBlackListFilter({ filter: newFilter }));
  };
  return (
    <>
      <TableWithColumnVisibility
        columns={columns}
        dataSource={blackLists?.data?.Value}
        loading={blackLists.isLoading||blackLists.isFetching}
        onRow={(record:any) => {
          return {
            onClick: async(event:any) => {
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            onChange: handleChangePagination,
            total: blackLists.data?.FilteredCount || 0,
            current: blackLists.data?.PageNumber,
            pageSize: blackLists.data?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e:any) => `${e}`,
          }}
        rowKey={"Id"}
      />
      <Drawer
        title={t("blackList.editBlackList")}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateBlackList
          selectedRecord={selectedRecord}
          onFinish={() => setIsShowEditDrawer(false)}
        />
      </Drawer>
      
    </>
  );
};

export default ListItems;
