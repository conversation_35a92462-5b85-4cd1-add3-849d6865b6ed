import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";

import GeneralCustomerWithSearch from "@/apps/Common/GeneralCustomerWIthSearch";
import { determineBlackListChannel, determineBlackListContactType } from "@/helpers/BlackList";
import { hanldleSetBlackListFilter } from "../ClientSideStates";
import { useParams, useSearchParams } from "react-router-dom";

const DetailsFilter: FC<{ onFinish: any }> = ({ onFinish }) => {
  const { filter } = useSelector((state: RootState) => state.calander);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const contactTypeOptions = determineBlackListContactType("select", null, t);
  const channelsOptions = determineBlackListChannel("select", null, t);
  const { formActions, mazakaForm } = useMazakaForm(form);
   const [searchParams] = useSearchParams();
        const params = useParams();
        const customerId =
          searchParams.get("customerId") || params["customerId"] || "";
  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
 

  

    let currentFilter = { ...filter };

    for (let key in formValues) {
      if (
        formValues[key]!==0&&( !formValues[key] ||
          (Array.isArray(formValues[key]) && formValues[key].length === 0))
      ) {
        delete formValues[key];
        delete currentFilter[key];
      }
    }
   
    const newFilter = { ...currentFilter, ...formValues };

    await dispatch(
      hanldleSetBlackListFilter({
        filter: newFilter,
      })
    );
    mazakaForm.setSuccess(1000, () => {}, t("form.transactionSuccessful"));
    onFinish();
  };

  useEffect(() => {
    
 
    
      form.setFieldsValue({
       CustomerId:filter?.CustomerId,
       ContactType:filter?.ContactType,
       BlockedChannels:filter?.BlockedChannels,
      
     
        
      });
    
  }, [filter]);

  
  return (
    <>
      <MazakaForm
        form={form}
        submitButtonVisible={false}
        onFinish={handleOnFinish}
      >
        <Row gutter={[20, 20]}>
          {
            !customerId&&
          <GeneralCustomerWithSearch
            label={t("blackList.customer")}
            placeholder={t("blackList.customer")}
            name="CustomerId"
            xs={24}
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameCustomerId", obj?.label);
            }}
          />
          }
          <MazakaSelect
            label={t("blackList.contactType")}
            placeholder={t("blackList.contactType")}
            xs={24}
            name="ContactType"
          
            options={contactTypeOptions}
          />
          
          <MazakaSelect
            label={t("blackList.blockedChannels")}
            placeholder={t("blackList.blockedChannels")}
            xs={24}
            name="BlockedChannels"
           
            options={channelsOptions}
          />
         


         
          <Col xs={24}>
            <MazakaButton
              htmlType="submit"
              processType={formActions.submitProcessType}
              status="save"
            >
              {t("users.filter.filterButton")}
            </MazakaButton>

          </Col>
           <Form.Item name={"customNameContactType"} className="!hidden"></Form.Item>
          <Form.Item name={"customNameBlockedChannels"} className="!hidden"></Form.Item>
          <Form.Item name={"customNameCustomerId"} className="!hidden"></Form.Item>
        </Row>
      </MazakaForm>
    </>
  );
};

export default DetailsFilter;
