import { <PERSON>, Divide<PERSON>, Drawer, <PERSON> } from "antd";
import { useState } from "react";
import Search from "./Search";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { ClearOutlined, PlusOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import AddOrUpdateBlackList from "./AddOrUpdateBlackList";
import GeneralFilterButton from "@/apps/Common/GeneralFilterButton";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import FilterTags from "./FilterTags";
import DetailsFilter from "./DetailsFilter";
import { useParams, useSearchParams } from "react-router-dom";

const TopOptions = () => {
  const [isShowAddDrawer, setIsShowAddDrawer] = useState(false);
  const { filter } = useSelector((state: RootState) => state.blackList);
  const { t } = useTranslation();
  const [isShowDetailsFilterDrawer, setIsShowDetailsFilterDrawer] =
    useState(false);
      const [searchParams] = useSearchParams();
      const params = useParams();
      const customerId =
        searchParams.get("customerId") || params["customerId"] || "";
  return (
    <>
      <Row>
        <>
          <Col xs={24}>
            <Search />
          </Col>

          <Col xs={24}>
            <Divider className="!m-0 !text-gray-400" />
          </Col>
        </>

        <div className=" !py-1 !px-2 flex gap-1">
          <MazakaButton
            icon={<PlusOutlined />}
            onClick={() => {
              setIsShowAddDrawer(true);
            }}
          >
            {t("blackList.add")}
          </MazakaButton>
          <GeneralFilterButton
            selectedIds={[]}
            onClick={() => {
              setIsShowDetailsFilterDrawer(true);
            }}
            title={t("users.filter.filterButton")}
          />
          {(
  customerId
    ? filter && Object.entries(filter).length > 3
    : filter && Object.entries(filter).length > 2
) && (
  <>
    <MazakaButton
      status="save"
      type="text"
      onClick={() => {
        // dispatch(
        //   handleResetFilterBlackList()
        // );
      }}
      icon={<ClearOutlined />}
    >
      {t("clearFilterButton")}
    </MazakaButton>
  </>
)}

        </div>
        <Col xs={24}>
          <Divider className="!m-0" />
        </Col>
         <FilterTags />
      </Row>
      <Drawer
        title={t("blackList.addBlackList")}
        open={isShowAddDrawer}
        onClose={() => {
          setIsShowAddDrawer(false);
        }}
      >
        <AddOrUpdateBlackList
          onFinish={() => {
            setIsShowAddDrawer(false);
          }}
        />
      </Drawer>

      <Drawer
        title={t("customers.filter.filterData")}
        open={isShowDetailsFilterDrawer}
        onClose={() => {
          setIsShowDetailsFilterDrawer(false);
        }}
      >
        <DetailsFilter
          onFinish={() => {
            setIsShowDetailsFilterDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default TopOptions;
