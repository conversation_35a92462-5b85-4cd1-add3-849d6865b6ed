import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";

import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { createBlackList, updateBlackListWithPut } from "../Services";
import {
  determineBlackListChannel,
  determineBlackListContactType,
} from "@/helpers/BlackList";
import { useTranslation } from "react-i18next";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";
import dayjs from "dayjs";
import GeneralCustomerWithSearch from "@/apps/Common/GeneralCustomerWIthSearch";
import { useParams, useSearchParams } from "react-router-dom";
import { useGetCustomerDetailsByType } from "../../TempCustomer/ServerSideStates";

const AddOrUpdateBlackList: FC<{
  onFinish: () => void;
  selectedRecord?: any;
}> = ({ onFinish, selectedRecord }) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const contactTypeOptions = determineBlackListContactType("select", null, t);
  const channelsOptions = determineBlackListChannel("select", null, t);
  const [searchParams] = useSearchParams();
  const params = useParams();
  const customerId =
    searchParams.get("customerId") || params["customerId"] || "";
    const customerDetails = useGetCustomerDetailsByType(
      customerId,
      searchParams.get("type") === "tempCustomer" ? "tempCustomer" : "customer"
    );

    useEffect(()=>{
      if(customerId&&customerDetails?.data&&!selectedRecord)
      {
        form.setFieldsValue({
          CustomerId:customerId,
          ContactType:2,
          ContactValue:customerDetails?.data?.Value?.Phone
        })
      }
    },[customerDetails.data,customerId])

  useEffect(() => {
    if (selectedRecord) {
      let data = { ...selectedRecord };
      if (data["ExpiryDate"]) {
        data["ExpiryDate"] = dayjs(data["ExpiryDate"]);
      }
      form.setFieldsValue({ ...data });
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    formValues["CustomerId"] = customerId
      ? customerId
      : formValues["CustomerId"];
    if (formValues["ExpiryDate"]) {
      formValues["ExpiryDate"] = dayjs(formValues["ExpiryDate"]);
    }
    try {
      if (selectedRecord) {
        await updateBlackListWithPut({ ...selectedRecord, ...formValues });
      } else {
        await createBlackList(formValues);
      }
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getBlackListListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
        initialValues={{ BlockedChannels: 7 }}
      >
        <Row gutter={[20, 20]}>
          <GeneralCustomerWithSearch
            label={t("blackList.customer")}
            placeholder={t("blackList.customer")}
            xs={24}
            name="CustomerId"
            allowClear
            onChange={(value: string, obj: any) => {
              if (value) {
                form.setFieldsValue({
                  ContactType: 2,
                  ContactValue: obj?.Phone,
                });
              } else {
                form.resetFields(["ContactType", "ContactValue"]);
              }
            }}
          />
          <MazakaSelect
            label={t("blackList.contactType")}
            placeholder={t("blackList.contactType")}
            xs={24}
            name="ContactType"
            rules={[{ required: true, message: "" }]}
            options={contactTypeOptions}
            onChange={() => {
              form.resetFields(["ContactValue"]);
            }}
          />
          <MazakaInput
            label={t("blackList.contactValue")}
            placeholder={t("blackList.contactValue")}
            xs={24}
            name="ContactValue"
            rules={[{ required: true, message: "" }]}
          />

          <MazakaSelect
            label={t("blackList.blockedChannels")}
            placeholder={t("blackList.blockedChannels")}
            xs={24}
            name="BlockedChannels"
            rules={[{ required: true, message: "" }]}
            options={channelsOptions}
          />
          <MazakaDatePicker
            tooltip={t("blackList.expiryDateDesc")}
            disablePastDates={true}
            disablePastTimes={true}
            label={t("blackList.expiryDate")}
            name="ExpiryDate"
            xs={24}
          />

          <MazakaTextArea
            label={t("blackList.reason")}
            placeholder={t("blackList.reason")}
            xs={24}
            name="Reason"
          />

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {selectedRecord ? t("blackList.edit") : t("blackList.add")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateBlackList;
