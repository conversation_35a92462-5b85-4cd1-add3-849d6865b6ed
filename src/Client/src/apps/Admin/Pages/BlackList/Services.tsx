import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getBlackListListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getBlackListListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const checkOnBlackLis= async (data:any): Promise<DataResponse<any>> => {
  const url = `${endpoints.checkOnBlackList}/check`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data,config);
};

export const createBlackList = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createBlackList}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateBlackListWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateBlackListWithUrl}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteBlackList = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteBlackList}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
