import { Col, Row } from "antd";

import { useQueryClient } from "react-query";
import { FC, useEffect } from "react";
import endPoints from "./EndPoints";

import TopOptions from "./Components/TopOptions";
import Title from "./Components/Title";
import ListItems from "./Components/ListItem";
import { useDispatch } from "react-redux";
import { useParams, useSearchParams } from "react-router-dom";
import { hanldleSetBlackListFilter } from "./ClientSideStates";

const BlackListIndex: FC<{ pageType: "customer" | "blackList" }> = ({
  pageType,
}) => {
  let dispatch = useDispatch();

  const [searchParams] = useSearchParams();
  const params = useParams();
  const customerId =
    searchParams.get("customerId") || params["customerId"] || "";
  const queryClient = useQueryClient();
  useEffect(() => {
    queryClient.resetQueries({
      queryKey: endPoints.getBlackListListFilter,
      exact: false,
    });
  }, []);

  useEffect(() => {
    if (customerId) {
      let filter = { PageNumber: 1, PageSize: 30, CustomerId: customerId };
      dispatch(hanldleSetBlackListFilter({ filter }));
    } else {
      let filter = { PageNumber: 1, PageSize: 30 };
      dispatch(hanldleSetBlackListFilter({ filter }));
    }
  }, [customerId]);

  return (
    <>
      <Col xs={24}>
        <Row gutter={[0, 0]}>
          {pageType === "blackList" && (
            <Col xs={24}>
              <Title />
            </Col>
          )}
          <Col xs={24}>
            <TopOptions />
          </Col>
          <Col xs={24} className="!px-2">
            <ListItems />
          </Col>
        </Row>
      </Col>
    </>
  );
};

export default BlackListIndex;
