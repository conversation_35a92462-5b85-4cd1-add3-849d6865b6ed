
import { Col,  Form, Row, Typography } from "antd";

import InternalComment from "./InternalComments";
import AutoDialerName from "./AutoDialerName";
import { useParams, useSearchParams } from "react-router-dom";
import AutoDialerCalendar from "@/apps/Admin/Pages/Calendar/Components/Calendar/AutoDialerCalendar";

const TakeNotes = () => {
const params = useParams();
  const [searchParams] = useSearchParams();
  const customerId = params["customerId"] || searchParams.get("customerId");

  return (
    <Row
      className="!h-screen !border-gray-100 !bg-gray-100 "
      style={{ borderRight: "1px solid gray" }}
    >
      <Col xs={24}>
       
          <Row gutter={[0, 10]} className="!px-2 !py-2">
            <Col xs={24}>
              
              <AutoDialerName/>
             
              <Col xs={24} className="">
                  <InternalComment />
                </Col>
                {
                 !customerId&&
                  <Col xs={24} className="!mt-6">
                <AutoDialerCalendar/>
                </Col>
                }
              
            </Col>
          </Row>
       
      </Col>
    </Row>
  );
};

export default TakeNotes;
