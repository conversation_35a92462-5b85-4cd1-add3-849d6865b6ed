import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Typography } from "antd";

import GeneralInfoes from "./Components/GeneralInfoes";
import TakeNotes from "./Components/TakeNotes";
import {  useEffect, useState } from "react";
import { EditOutlined, UserOutlined } from "@ant-design/icons";
import AddOrUpdateCustomerIndex from "../../../Customers/Components/AddOrUpdate/AddOrUpdateCustomerIndex";
import { useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";


const AutoDailerCallIndex = () => {
  const [isShowGeneralInfoes, setIsShowGeneralInfoes] = useState(true);
  const [searchParams] = useSearchParams();
  const customerId = searchParams.get("customerId");
const {t} = useTranslation()
  const [isShowExternalNote, setIsShowExternalNote] = useState(true);
  const wrapperFormWidth = () => {
    if (!isShowGeneralInfoes && !isShowExternalNote) {
      return 24;
    } else if (!isShowGeneralInfoes || !isShowExternalNote) {
      return 19;
    }
    return 16;
  };

  useEffect(() => {
    if (!customerId) {
      setIsShowGeneralInfoes(false);
    }
    else{
      setIsShowGeneralInfoes(true);
    }
  }, [customerId]);

  return (
    <>
          <Col xs={24}>
            <Row gutter={[0, 0]}>
              <Col xs={24} className="">
                <Row gutter={[0, 0]} className="">
                  <Col xs={24}>
                    <Divider className="!m-0  " />
                  </Col>
                </Row>
              </Col>

              {isShowGeneralInfoes && (
                <>
                  <Col
                    xs={24}
                    xl={!customerId ? 6 : isShowExternalNote ? 4 : 5}
                  >
                    <GeneralInfoes mode={!customerId ? "add" : "edit"} />
                  </Col>
                </>
              )}
              {isShowExternalNote && (
                <>
                <Col xs={24} xl={isShowGeneralInfoes ? 4 : 5} className="">
                  <TakeNotes />
                </Col>
               
                </>

              )}
              <Col xs={24} xl={wrapperFormWidth()}>
                <AddOrUpdateCustomerIndex type="drawer"  />
              </Col>
            </Row>
          </Col>
          <FloatButton.Group shape="square" style={{ insetInlineEnd: "97%" }}>
            {customerId && (
              <Tooltip
                title={
                  isShowGeneralInfoes
                    ? t("callNotification.list.closeCustomerInfoes")
                    :  t("callNotification.list.openCustomerInfoes")
                }
              >
                <FloatButton
                  onClick={() => {
                    setIsShowGeneralInfoes(!isShowGeneralInfoes);
                  }}
                  icon={<UserOutlined />}
                />
              </Tooltip>
            )}

            <Tooltip
              title={isShowExternalNote ? t("callNotification.list.closeTakeNote"):  t("callNotification.list.openTakeNote")}
            >
              <FloatButton
                icon={<EditOutlined />}
                onClick={() => {
                  setIsShowExternalNote(!isShowExternalNote);
                }}
              />
            </Tooltip>
          </FloatButton.Group>
     
    </>
  );
};

export default AutoDailerCallIndex;
