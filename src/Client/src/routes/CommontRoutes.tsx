import LogsIndex from "@/apps/Admin/Pages/AuditLogs/LogsIndex";
import AddOrUpdateAuthorityIndex from "@/apps/Admin/Pages/Authority/Components/AddOrUpdate/AddOrUpdateAuthorityIndex";
import OtoDailerIndex from "@/apps/Admin/Pages/AutoDialer/AutoDialerIndex";
import AddOrUpdateOtoDailerIndex from "@/apps/Admin/Pages/AutoDialer/Components/AddOrUpdate/AddOrUpdateIndex";
import ClassificationIndex from "@/apps/Admin/Pages/Classification/ClassificationIndex";
import AddOrUpdateCustomerIndex from "@/apps/Admin/Pages/Customers/Components/AddOrUpdate/AddOrUpdateCustomerIndex";
import CustomerIndex from "@/apps/Admin/Pages/Customers/CustomerIndex";
import DepartmentIndex from "@/apps/Admin/Pages/Department/DepartmentIndex";
import NotificationWayIndex from "@/apps/Admin/Pages/NotificationWay/NotificationWayIndex";
import PauseTypeIndex from "@/apps/Admin/Pages/PauseType/PauseTypeIndex";
import ProfessionIndex from "@/apps/Admin/Pages/Profession/ProfessionIndex";
import RecordingsIndex from "@/apps/Admin/Pages/Recordings/RecordingIndex";
import MyReportIndex from "@/apps/Admin/Pages/Report/Pages/MyReport/MyReportIndex";
import AddOrUpdateReprotTypes from "@/apps/Admin/Pages/Report/Pages/ReportTypes/Components/AddOrUpdate/AddOrUpdateReportTypesIndex";
import ReportTypesIndex from "@/apps/Admin/Pages/Report/Pages/ReportTypes/ReportTypesIndex";
import ReportIndex from "@/apps/Admin/Pages/Report/ReportIndex";
import RoleIndex from "@/apps/Admin/Pages/Role/RoleIndex";
import SectorIndex from "@/apps/Admin/Pages/Sector/SectorIndex";
import TaskIndex from "@/apps/Admin/Pages/Task/TaskIndex";
import TicketIndex from "@/apps/Admin/Pages/Ticket/TicketIndex";
import SubjectTicketIndex from "@/apps/Admin/Pages/TicketSubject/SubjectTicketIndex";
import AddOrUpdateUserIndex from "@/apps/Admin/Pages/Users/<USER>/AddOrUpdate.tsx/AddOrUpdateUserIndex";
import UsersIndex from "@/apps/Admin/Pages/Users/<USER>";
import AddOrUpdateWorkflowIndex from "@/apps/Admin/Pages/WorkFlow/Components/AddOrUpdate/AddOrUpdateWorkflowIndex";
import WorkflowIndex from "@/apps/Admin/Pages/WorkFlow/WorkflowIndex";
import CallIndex from "@/apps/Call/CallIndex";
import LanguageIndex from "@/apps/Language/LanguageIndex";
import PausesIndex from "@/apps/Pauses/PausesIndex";
import TempCustomerIndex from "@/apps/Admin/Pages/TempCustomer/TempCustomerIndex";
import AddOrUpdateTempCustomerIndex from "@/apps/Admin/Pages/TempCustomer/AddOrUpdate/AddTempCustomerIndex";
import { Route } from "react-router-dom";
import AdminIndex from "@/apps/Admin/AdminIndex";
import { commonRoutePrefix } from "./Prefix";
import CustomerSourceIndex from "@/apps/Admin/Pages/CustomerSource/CustomerSourceIndex";
import ThreeCXQueuesIndex from "@/apps/Admin/Pages/ThreeCXQueues/ThreeCXQueuesIndex";
import NotesIndex from "@/apps/Admin/Pages/Notes/NotesIndex";
import FileManagerIndex from "@/apps/FileManager/FileManagerIndex";
import FormIndex from "@/apps/Form/FormIndex";
import FormAttrIndex from "@/apps/Form/Components/FormAttr/FormAttrIndex";
import TemplateIndex from "@/apps/Admin/Pages/Template/TemplateIndex";
import CalendarNotesIndex from "@/apps/Admin/Pages/Calendar/CalendarNotesIndex";
import ResourceIndex from "@/apps/Admin/Pages/Resources/ResourcesIndex";
import BlackListIndex from "@/apps/Admin/Pages/BlackList/BlackListIndex";




const commonRoutes = [
  { path: "users", element: <UsersIndex /> },
  { path: "logs", element: <LogsIndex /> },
  { path: "add-user", element: <AddOrUpdateUserIndex /> },
  { path: "edit-user/:userId", element: <AddOrUpdateUserIndex /> },
  { path: "pauses", element: <PausesIndex role="admin" /> },
  { path: "pause-types", element: <PauseTypeIndex /> },
  { path: "customers", element: <CustomerIndex /> },
  { path: "notification-ways", element: <NotificationWayIndex /> },
  { path: "call-reports", element: <CallIndex /> },
  { path: "add-customer", element: <AddOrUpdateCustomerIndex type="url" /> },
  { path: "edit-customer/:customerId", element: <AddOrUpdateCustomerIndex type="url" /> },
  { path: "edit-temp-customer", element: <AddOrUpdateCustomerIndex type="url" /> },
  { path: "add-auto-dialer", element: <AddOrUpdateOtoDailerIndex /> },
  { path: "edit-auto-dialer/:autoDialerId", element: <AddOrUpdateOtoDailerIndex /> },
  { path: "workflow", element: <WorkflowIndex /> },
  { path: "edit-workflow/:workFlowId", element: <AddOrUpdateWorkflowIndex /> },
  { path: "reports", element: <ReportIndex /> },
  { path: "customer-sources", element: <CustomerSourceIndex /> },
  { path: "recordings", element: <RecordingsIndex /> },
  { path: "reprort/my-report", element: <MyReportIndex /> },
  { path: "reprort/report-types", element: <ReportTypesIndex /> },
  { path: "professions", element: <ProfessionIndex /> },
   { path: "black-list", element: <BlackListIndex pageType={"blackList"}  /> },
  { path: "resources", element: <ResourceIndex /> },
  { path: "file-manager", element: <FileManagerIndex isShowTitle={true} onFinishSelectFile={()=>{}}  /> },
  { path: "languages", element: <LanguageIndex /> },
  { path: "roles", element: <RoleIndex /> },
  { path: "subject-ticket", element: <SubjectTicketIndex /> },
  { path: "sectors", element: <SectorIndex /> },
  { path: "templates", element: <TemplateIndex /> },
  { path: "calendar-notes", element: <CalendarNotesIndex /> },
  { path: "forms", element: <FormIndex/> },
  { path: "form-attr-details/:formId", element: <FormAttrIndex/> },
  { path: "classifications", element: <ClassificationIndex /> },
  { path: "user-department", element: <DepartmentIndex /> },
  { path: "tickets", element: <TicketIndex mode="customer" pageType="ticket" /> },
  { path: "auto-dailer", element: <OtoDailerIndex /> },
  { path: "import-data", element: <TempCustomerIndex /> },
  { path: "tasks", element: <TaskIndex /> },
  { path: "authority", element: <AddOrUpdateAuthorityIndex /> },
  { path: "add-import-data", element: <AddOrUpdateTempCustomerIndex pageType="url" isAutoDialer={false} /> },
  { path: "report/add-report-types", element: <AddOrUpdateReprotTypes /> },
  
  {
    path: "threecx-queues", element: <ThreeCXQueuesIndex />
  },
  {
    path: "notes", element: <NotesIndex />
  }

];

export const commonRouteList = (
  <Route key="commonRouteList">
    <Route path={commonRoutePrefix} element={<AdminIndex />}>
      {commonRoutes.map((route, idx) => (
        <Route
          key={idx}
          path={`${commonRoutePrefix}/${route.path}`}
          element={route.element}
        />
      ))}
    </Route>
   
  </Route>
);


