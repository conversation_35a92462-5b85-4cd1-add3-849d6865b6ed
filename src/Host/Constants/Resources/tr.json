{"DefaultRole": "<PERSON><PERSON>", "FileUpload": "<PERSON><PERSON><PERSON>", "FileUploadSuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>ü<PERSON>.", "InvalidFileFormat": "Geçersiz dosya formatı.", "MissingRequiredField": "<PERSON><PERSON><PERSON><PERSON> alan <PERSON>.", "UnauthorizedAccess": "Bu işlemi gerçekleştirmek için yetkiniz yok.", "FileNotFound": "<PERSON><PERSON><PERSON> b<PERSON>.", "FileTooLarge": "<PERSON><PERSON><PERSON> boyutu <PERSON> büyü<PERSON>.", "InvalidFileType": "Geçersiz dosya türü.", "UploadFailed": "<PERSON><PERSON><PERSON> yü<PERSON>me başarısız oldu.", "FileAlreadyExists": "<PERSON><PERSON><PERSON> zaten mevcut.", "FileUploadInProgress": "<PERSON><PERSON><PERSON>.", "FileUploadCanceled": "<PERSON><PERSON><PERSON> iptal edildi.", "FileUploadCompleted": "<PERSON><PERSON><PERSON> ta<PERSON>.", "FileUploadFailed": "<PERSON><PERSON><PERSON> yü<PERSON>me başarısız oldu.", "FileUploadPaused": "<PERSON><PERSON><PERSON> y<PERSON> duraklatıldı.", "FileUploadResumed": "<PERSON><PERSON><PERSON> de<PERSON>m et<PERSON>.", "FileUploadRestarted": "<PERSON><PERSON><PERSON> yü<PERSON>me yeniden başlatıldı.", "FileUploadAborted": "<PERSON><PERSON><PERSON>.", "FileUploadTimeout": "<PERSON><PERSON><PERSON> yü<PERSON>me zaman aşımına uğradı.", "FileUploadError": "<PERSON><PERSON><PERSON> hatası.", "FileUploadNotSupported": "<PERSON><PERSON><PERSON>.", "FileUploadNotAllowed": "<PERSON><PERSON><PERSON> y<PERSON>meye izin verilmiyor.", "FileUploadLimitExceeded": "Dosya yükleme limiti aşıldı.", "FileUploadInvalid": "Geçersiz <PERSON>.", "FileUploadSuccessful": "<PERSON><PERSON><PERSON> başarılı.", "FileExtensionNotSupported": "Sadece {{Extensions}} uzantıları destekleniyor.", "FileSizeExceeded": "Yüklenen dosya {{MaxMb}}MB'ı geçemez.", "Users.Status.Active": "Aktif", "Users.Status.Inactive": "<PERSON><PERSON><PERSON>", "User.NotFound": "Kullanıcı bulunamadı.", "ExcelUserColumns.Name": "Ad", "ExcelUserColumns.Surname": "Soyad", "ExcelUserColumns.Email": "E-posta", "ExcelUserColumns.Phone": "Telefon", "ExcelUserColumns.Company": "Şirket", "ExcelUserColumns.Position": "Pozisyon", "ExcelUserColumns.Address": "<PERSON><PERSON>", "ExcelUserColumns.City": "Şehir", "ExcelUserColumns.ExtensionNo": "Dahili No", "ExcelUserColumns.Departman": "<PERSON><PERSON><PERSON>", "ExcelUserColumns.Group": "Grup", "ExcelUserColumns.Active": "Durum(0:<PERSON><PERSON><PERSON>, 1:<PERSON><PERSON><PERSON>)", "ExcelUserColumns.Password": "Şifre", "ExcelUserColumns.InsertDate": "<PERSON><PERSON><PERSON>", "ExcelReadError": "Excel dosyasından veri okunamadı.", "InvalidExcelData": "Excel dosyası, modele uygun veri içermiyor.", "NoNewUsersToImport": "İçe aktarılacak yeni kullanıcı bulunamadı.", "Row": "Satır", "UnexpectedError": "Beklenmedik bir hata <PERSON>.", "NameRequired": "<PERSON>sim alanı boş olamaz.", "SurnameRequired": "<PERSON><PERSON><PERSON><PERSON> alanı boş olamaz.", "EmailRequired": "E-posta alanı boş olamaz.", "EmailFormatInvalid": "Geçerli bir e-posta adresi giriniz.", "PhoneRequired": "Telefon alanı boş olamaz.", "ExtentionNoRequired": "<PERSON><PERSON><PERSON> numara alanı bo<PERSON> olamaz.", "ExtentionNoFormatInvalid": "<PERSON><PERSON><PERSON> numara yalnız<PERSON> rakam<PERSON>an oluşmalıdır.", "ExcelMappingError": "Satır {{Row}}: '{{Column}}' sütunundaki '{{Value}}' de<PERSON><PERSON>, '{{Property}}' <PERSON><PERSON><PERSON><PERSON>.", "Import.ValidationError": "{{Row}}. sat<PERSON><PERSON> hata: {{Detail}}", "Import.AlreadyExists": "Satır {{Row}}: '{{Email}}' ad<PERSON>i sistemde zaten mevcut.", "Import.ExtensionAlreadyExists": "Satır {{Row}}: '{{Extension}}' da<PERSON><PERSON> numa<PERSON> sistemde zaten mevcut.", "Import.ExtensionAlreadyPhoneExists": "Satır {{Row}}: '{{Em<PERSON>}}', '{{Phone}}' adresi sistemde zaten mevcut.", "Import.PropertyValidationError": "{{Row}}. satırda '{{Property}}' alan<PERSON> hatalı: {{Detail}}", "UserImport.ValidationFailed": "Kullanıcı içe aktarımı başarısız oldu. Lütfen geçerli veriler sağladığınızdan emin olun ve tekrar deneyin.", "UserImport.AlreadyExists": "Satır {{Row}}: '{{Email}}' ad<PERSON>i sistemde zaten mevcut.", "UserImport.ExtensionAlreadyExists": "Satır {{Row}}: '{{Extension}}' da<PERSON><PERSON> numa<PERSON> sistemde zaten mevcut.", "UserImport.ValidationError": "Satır {{Row}}: '{{Detail}}' hat<PERSON><PERSON>.", "ExcelCustomerColumns.Name": "Ad", "ExcelCustomerColumns.Surname": "Soyad", "ExcelCustomerColumns.Email": "E-posta", "ExcelCustomerColumns.Phone": "Telefon", "ExcelCustomerColumns.PhonePrefix": "<PERSON><PERSON><PERSON>", "ExcelUserColumns.PhonePrefix": "<PERSON><PERSON><PERSON>", "ExcelCustomerColumns.Type": "Müşteri Türü: Individual, Corporate", "ExcelCustomerColumns.Kind": "Müşteri Tipi: <PERSON>er, <PERSON>tentialCust<PERSON>, <PERSON>w", "ExcelCustomerColumns.Status": "Durum: Active, Inactive, Suspended", "ExcelCustomerColumns.Advisor": "<PERSON><PERSON><PERSON><PERSON>", "ExcelCustomerColumns.Classification": "Sınıflandırma", "ExcelCustomerColumns.Source": "<PERSON><PERSON><PERSON>", "ExcelCustomerColumns.Country": "<PERSON><PERSON><PERSON>", "ExcelCustomerColumns.Address": "<PERSON><PERSON>", "ExcelCustomerColumns.State": "Eyalet", "ExcelCustomerColumns.City": "Şehir", "ExcelCustomerColumns.Province": "İlçe", "ExcelCustomerColumns.PostCode": "Posta Kodu", "ExcelCustomerColumns.TaxNumber": "Vergi No", "ExcelCustomerColumns.TaxOffice": "<PERSON><PERSON><PERSON>", "ExcelCustomerColumns.IdentificationNumber": "<PERSON><PERSON>", "ExcelCustomerColumns.Language": "Dil", "ExcelCustomerColumns.AvailableLanguage": "Mevcut Dil", "ExcelCustomerColumns.Description": "<PERSON><PERSON>ı<PERSON><PERSON>", "ExcelCustomerColumns.MailBcc": "Mail Bcc", "ExcelCustomerColumns.CustomerSource": "Müşteri Kaynağı", "ExcelCustomerColumns.NotificationWay": "<PERSON><PERSON><PERSON><PERSON>", "ExcelCustomerDto.NameEmpty": "Ad alanı boş olamaz.", "ExcelCustomerDto.NameMaxLength": "Ad alanı 100 karakterden uzun olamaz.", "ExcelCustomerDto.SurnameEmpty": "Soyad alanı boş olamaz.", "ExcelCustomerDto.SurnameMaxLength": "Soyad alanı 100 karakterden uzun olamaz.", "ExcelCustomerDto.EmailEmpty": "<PERSON><PERSON> al<PERSON> boş o<PERSON>az.", "ExcelCustomerDto.EmailInvalid": "Geçersiz email formatı.", "ExcelCustomerDto.PhoneEmpty": "Telefon alanı boş olamaz.", "ExcelCustomerDto.PhoneInvalid": "Geçersiz telefon numarası formatı.", "ExcelCustomerDto.TypeEmpty": "<PERSON><PERSON><PERSON> al<PERSON> boş o<PERSON>az.", "ExcelCustomerDto.TypeInvalid": "Geçersiz tür. Geçerli türler: Individual, Corporate.", "ExcelCustomerDto.StatusInvalid": "Geçersiz durum. Geçerli durumlar: Active, Inactive, Suspended.", "ExcelCustomerDto.KindInvalid": "Geçersiz tür. Geçerli türler: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>.", "ExcelCustomerDto.CountryMaxLength": "Ülke adı 100 karakterden uzun olamaz.", "ExcelCustomerDto.LanguageMaxLength": "Dil adı 50 karakterden uzun olamaz.", "ExcelCustomerDto.DescriptionMaxLength": "Açıklama 500 karakterden uzun olamaz.", "ExcelCustomerDto.TaxOfficeMaxLength": "<PERSON>ergi da<PERSON>i 100 karakterden uzun olamaz.", "ExcelCustomerDto.TaxNumberMaxLength": "Vergi numarası 50 karakterden uzun olamaz.", "ExcelCustomerDto.IdentificationNumberMaxLength": "Kimlik numarası 50 karakterden uzun olamaz.", "ExcelCustomerDto.MailBccMaxLength": "BCC alanı 100 karakterden uzun olamaz.", "ExcelCustomerDto.ClassificationMaxLength": "Sınıflandırma adı 100 karakterden uzun olamaz.", "ExcelCustomerDto.CustomerSourceMaxLength": "Müşteri kaynağı 100 karakterden uzun olamaz.", "ExcelCustomerDto.AdvisorEmailInvalid": "Geçersiz <PERSON>şman email adresi.", "ExcelCustomerDto.NotificationWayMaxLength": "Bildirim yöntemi 100 karakterden uzun olamaz.", "CustomerImport.ValidationFailed": "Müşteri içe aktarımı başarısız oldu. Lütfen geçerli veriler sağladığınızdan emin olun ve tekrar deneyin.", "CustomerImport.AlreadyExists": "Satır {{Row}}: '{{Email}}' ad<PERSON>i sistemde zaten mevcut.", "CustomerImport.ExtensionAlreadyPhoneExists": "Satır {{Row}}: '{{Em<PERSON>}}', '{{Phone}}' adresi sistemde zaten mevcut.", "CustomerImport.AdvisorNotFound": "{{Row}} n<PERSON><PERSON><PERSON> sat<PERSON>, {{AdvisorEmail}} email adresine sahip bir dan<PERSON><PERSON>man bulunamadı.", "CustomerImport.ValidationFailedDetail": "Lütfen aşağıdaki hataları düzelterek tekrar deneyiniz.", "Customers.NotFound": "Müşteri bulunamadı.", "TempCustomer.Validation.NameSurnameRequired": "Ad ve soyad zorunludur.", "TempCustomer.Validation.EmailInvalid": "Geçersiz e-posta formatı: {Email}", "TempCustomer.Validation.PhoneInvalid": "Telefon numarası yalnızca rakamlardan oluşmalıdır.", "Export.LimitExceeded": "En fazla 10.000 kayıt dışa aktarılabilir.", "Export.Success": "Dışa aktarma işlemi başarılı.", "EmailPart.EmptyFallback": "_bos", "UnknownUser": "Bilinmeyen Kullanıcı", "Customers.BulkDeleteCustomer.NoIdsProvided": "Silinecek müşteri kimlikleri sağlanmadı.", "Customers.BulkDeleteCustomer.Failed": "Müşteri silme işlemi başarısız oldu. Lütfen geçerli müşteri kimlikleri sağladığınızdan emin olun ve tekrar deneyin.", "Customers.BulkDeleteCustomer.Success": "Seçilen müşteriler baş<PERSON><PERSON><PERSON> si<PERSON>.", "Customers.BulkDeleteCustomer.SomeIdsNotFound": "Silinecek bazı müşteri kimlikleri bulunamadı.", "Customers.DeleteCustomer.Failed": "Müşteri silme işlemi başarısız oldu. Lütfen geçerli müşteri kimliği sağladığınızdan emin olun ve tekrar deneyin.", "Customers.DeleteTempCustomer.Failed": "Geçici müşteri silme işlemi başarısız oldu. Lütfen geçerli müşteri kimliği sağladığınızdan emin olun ve tekrar deneyin.", "IdMismatch": "Sağlanan kimlik, beklenen kimlikle eşleşmiyor.", "FilterNotFound": "Filtre bulunamadı.", "UserNotFound": "Kullanıcı bulunamadı.", "FilterAlreadyExists": "Filtre adı '{Name}' ile zaten mevcut.", "Calendar.StartTimeWasInThePast": "Başlangıç tarihi geçmiş olamaz.", "Calendar.EndTimeWasBeforeStartTime": "Bitiş tarihi ba<PERSON>langıç tarihinden önce olamaz.", "Calendar.InvalidNoteType": "Geçersiz not tipi.", "Calendar.InvalidVisibility": "Geçersiz görünürlük tipi.", "Calendar.InvalidEventType": "Geçersiz etkinlik tipi.", "Calendar.RequiredCustomer": "Müşteri seçilmesi zorunludur.", "Calendar.Validation.MissingCustomerForAutoCall": "Otomatik arama tipi için müşteri seçimi zorunludur.", "Calendar.Validation.DepartmentRequired": "<PERSON><PERSON><PERSON> seçilmelid<PERSON>.", "Calendar.Validation.StartDateCannotBePast": "Başlangıç tarihi geçmiş olamaz.", "Calendar.Validation.EndDateBeforeStart": "<PERSON><PERSON>ş tarihi, b<PERSON><PERSON><PERSON><PERSON><PERSON> tarihinden önce olamaz.", "Calendar.Validation.EndDateMustBeAfterStartDate": "Bitiş tarihi ba<PERSON><PERSON><PERSON><PERSON> tarihinden sonra olmalıdır.", "Calendar.TagAlreadyExists": "'{Name}' is<PERSON><PERSON> bir etiket zaten mevcut.", "Calendar.Note.NotFound": "Belirtilen takvim notu bulunamadı.", "Calendar.Note.Unauthorized": "Bu takvim notu üzerinde işlem yapma yetkiniz bulunmamaktadır.", "Calendar.Note.DeleteSuccess": "Tak<PERSON>m notu ba<PERSON><PERSON><PERSON><PERSON> silind<PERSON>.", "Calendar.Note.UpdateSuccess": "Takvim notu başarıyla güncellendi.", "Calendar.Note.CreateSuccess": "Takvim notu başarıyla oluşturuldu.", "Calendar.Note.ListSuccess": "Takvim notları başarıyla getirildi.", "Calendar.Note.ValidationFailed": "Takvim notu doğrulama başarısız.", "Calendar.Note.TitleRequired": "Başlık boş olamaz.", "Calendar.Note.TitleMaxLength": "Başlık 500 karakterden uzun olamaz.", "Calendar.Note.DescriptionMaxLength": "Açıklama 2000 karakterden uzun olamaz.", "Calendar.Note.StartDateRequired": "Başlangıç tarihi boş olamaz.", "Calendar.Note.InvalidNoteType": "Geçersiz takvim notu tipi.", "Calendar.Note.InvalidVisibility": "Geçersiz görünürlük tipi.", "Calendar.Note.DepartmentRequiredForDepartmental": "Departmansal görünürlük için departman seçilmelidir.", "Calendar.Note.ReminderMinutesInvalid": "Hatırlatma süresi 0 veya pozitif olmalıdır.", "Calendar.Note.ReminderMinutesMaxLimit": "Hatırlatma süresi 30 günden (43200 dakika) fazla olamaz.", "Calendar.Note.InvalidReminderChannel": "Geçersiz hatırlatma kanalı.", "Calendar.Note.InvalidRecurrenceType": "Geçersiz tekrarlama tipi.", "Calendar.Note.RecurrenceIntervalInvalid": "Tekrarlama aralığı 0'dan büyük olmalıdır.", "Calendar.Note.RecurrenceIntervalMaxLimit": "Tekrarlama aralığı 365'ten küçük veya eşit olmalıdır.", "Calendar.Note.RecurrenceCountInvalid": "Tekrarlama sayısı 0'dan büyük olmalıdır.", "Calendar.Note.RecurrenceEndDateInvalid": "Tekrarlama bitiş tarihi gelecekte olmalıdır.", "Calendar.Note.PageNumberInvalid": "Sayfa numarası 0'dan büyük olmalıdır.", "Calendar.Note.PageSizeInvalid": "Sayfa boyutu 0'dan büyük olmalıdır.", "Calendar.Note.PageSizeMaxLimit": "Sayfa boyutu 100'den küçük veya eşit olmalıdır.", "Calendar.Note.SortDirectionInvalid": "Sıralama yönü 'asc' veya 'desc' olmalıdır.", "Calendar.Note.DateRangeInvalid": "Ba<PERSON><PERSON><PERSON><PERSON> tarihi, bitiş tarihinden küçük veya eşit olmalıdır.", "Calendar.UnknownDepartment": "Bilinme<PERSON><PERSON>", "Calendar.UnknownUser": "Bilinmeyen Kullanıcı", "Calendar.TagNotFound": "Belirtilen etiket bulunamadı.", "Calendar.TagInUse": "<PERSON>u etiket kullanımda olduğu için si<PERSON>.", "Calendar.Note.CompanyNotFound": "Belirtilen şirket bulunamadı.", "Calendar.Tag.NameRequired": "Etiket adı boş olamaz.", "Calendar.Tag.NameMaxLength": "Etiket adı 50 karakterden uzun olamaz.", "Calendar.Tag.NameUnique": "<PERSON>u isimde bir etiket zaten mevcut.", "Calendar.Tag.IdRequired": "Etiket ID'si boş olamaz.", "Calendar.Tag.NotFound": "Belirtilen etiket bulunamadı.", "Calendar.Tag.InUse": "<PERSON>u etiket kullanımda olduğu için si<PERSON>.", "Calendar.Note.IdRequired": "Takvim notu ID'si gereklidir.", "dashboard.topUsersByTaskCount": "G<PERSON>rev <PERSON>ına <PERSON>ö<PERSON>", "dashboard.dashboard": "Dashboard", "dashboard.statusOfTickets": "<PERSON>ick<PERSON><PERSON><PERSON>", "dashboard.priorityStatusTasks": "G<PERSON><PERSON>v<PERSON><PERSON>", "dashboard.unassigned": "Atanmamış", "dashboard.searchAssignmentGraph": "<PERSON><PERSON>", "dashboard.unAssignedDesc": "Görev henüz kimseye atanmadı", "dashboard.countinue": "<PERSON><PERSON>", "dashboard.countinueDesc": "<PERSON><PERSON><PERSON><PERSON>u anda devam ediyor", "dashboard.low": "Düşük", "dashboard.medium": "Orta", "dashboard.high": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.critical": "<PERSON><PERSON><PERSON>", "dashboard.done": "Tamamlandı", "dashboard.doneDesc": "Görev tamamlandı", "dashboard.urgent": "Acil", "dashboard.urgentDesc": "Görev tamamlandı", "recording.recordings": "<PERSON><PERSON>tlar", "recording.callerName": "<PERSON><PERSON>", "recording.all": "<PERSON><PERSON><PERSON>", "recording.caller": "<PERSON><PERSON>", "recording.callee": "Aranan", "recording.calleePhoneNumber": "Aranan Telefon Numarası", "recording.callerPhoneNumber": "Arayan Telefon Numarası", "recording.calleeName": "<PERSON>nan Adı", "recording.direction": "<PERSON><PERSON><PERSON>", "recording.inbound": "<PERSON><PERSON><PERSON>", "recording.outbound": "Giden", "recording.startTime": "Başlama Zamanı", "recording.endTime": "Bitiş Zamanı", "recording.saveQuickFilter": "Hızlı filtre kaydet", "recording.answeredTime": "Ceva<PERSON><PERSON><PERSON>", "recording.talkDurationInSeconds": "Konuşma <PERSON>i (saniye)", "recording.totalDurationInSeconds": "Toplam Süre (saniye)", "recording.copyDownloadLink": "İndirme Bağlantısını Kopyala", "recording.isAnswered": "Cevaplandı mı", "recording.extension": "<PERSON><PERSON><PERSON>", "recording.transcription": "Yazıya Döküm", "recording.export": "Dışa Aktar", "recording.delete": "Sil", "recording.missed": "Cevapsız", "recording.answerd": "Cevaplandı", "recording.callStatus": "<PERSON><PERSON>", "recording.showSummaryCall": "Aramanın <PERSON>tini <PERSON>", "recording.summary": "Özet", "recording.interviewSummary": "Gör<PERSON>ş<PERSON>", "recording.fullContent": "Tam <PERSON>k", "recording.download": "Download", "recording.invalidLink": "<PERSON>", "recording.pathNotFound": "Geçerli Bir Kayıt Mevcut Değil", "recording.callDetails": "Arama <PERSON>ı", "recording.sourceParticipantName": "Kaynak Katılımcı Adı", "recording.sourceParticipantPhoneNumber": "Kaynak Katılımcı TelefonNumarası", "general.pageNotFound": "Sayfa Bulunamadı", "general.gotoToDashboard": "<PERSON>", "general.forbiddenDesc": "Bu Sayfaya Erişim <PERSON>zniniz Bulunmuyor", "header.takeBreak": "<PERSON><PERSON> al", "header.myBreaks": "Molalarım", "header.avaliable": "<PERSON><PERSON>gun", "header.away": "Dışarıda", "header.doNotDistrub": "Rahatsız Etmeyin", "header.lunch": "<PERSON><PERSON><PERSON>", "header.businessTrip": "İş Seyahati", "header.logout": "Çıkış Yap", "header.settings": "<PERSON><PERSON><PERSON>", "account.login": "<PERSON><PERSON><PERSON>", "account.inCorrectEmailOrPass": "E-postanız veya şifreniz hatalı.", "account.email": "E-posta", "account.password": "Şifre", "account.forgotPassword": "Şif<PERSON><PERSON>", "account.sendEmail": "E-Posta Gönder", "account.emailSent": "E-Posta Gönderildi", "account.changePassword": "<PERSON><PERSON><PERSON><PERSON>", "account.newPassword": "<PERSON><PERSON>", "adminSidebar.notification&Tasks": "Bildirim ve Görevler", "adminSidebar.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adminSidebar.constant": "<PERSON><PERSON><PERSON>", "adminSidebar.callReports": "Çağrı Raporları", "adminSidebar.calendarNotes": "Takvi<PERSON>", "adminSidebar.fileManager": "<PERSON><PERSON><PERSON>", "adminSidebar.customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adminSidebar.profession": "Meslek", "adminSidebar.sectors": "Se<PERSON><PERSON><PERSON>", "adminSidebar.subjectTickets": "<PERSON><PERSON>", "adminSidebar.ticket": "Ticket", "adminSidebar.task": "<PERSON><PERSON><PERSON><PERSON>", "adminSidebar.form": "Form", "adminSidebar.classification": "Sınıflandırma", "adminSidebar.tag": "Etiket", "adminSidebar.calendar": "Takvim", "adminSidebar.importData": "<PERSON><PERSON>", "adminSidebar.autoDialer": "Otomatik Arayıcı", "adminSidebar.workflow": "İş Akışı", "adminSidebar.pauseManagement": "<PERSON><PERSON>", "adminSidebar.department": "<PERSON><PERSON><PERSON>", "adminSidebar.recordings": "<PERSON><PERSON>tlar", "adminSidebar.authority": "<PERSON><PERSON>", "adminSidebar.roles": "Roller", "adminSidebar.report": "<PERSON><PERSON>", "adminSidebar.languages": "<PERSON><PERSON>", "adminSidebar.customerSource": "Müşteri kaynağı", "adminSidebar.langResources": "Dil Kaynakları", "leftMainSidebar.admin": "Yönetici", "users.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "users.addButton": "<PERSON><PERSON>", "users.deleteAllButton": "Sil", "users.export": "Dışarıya Aktar", "users.assignRole": "Rol At", "users.searchPlaceholder": "Ara", "users.save": "<PERSON><PERSON>", "users.list.user": "Kullanıcı", "users.list.email": "E-posta", "users.list.extension": "<PERSON><PERSON><PERSON>", "users.list.department": "<PERSON><PERSON><PERSON>", "users.list.delete": "Sil", "users.list.edit": "<PERSON><PERSON><PERSON><PERSON>", "users.list.cancel": "Vazgeç", "users.list.warning": "Uyarı", "users.list.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "users.add.general": "<PERSON><PERSON>", "users.add.permissions": "<PERSON><PERSON><PERSON>", "users.add.options": "Seçenekler", "users.add.extension": "<PERSON><PERSON><PERSON>", "users.add.name": "Ad", "users.add.surName": "Soyad", "users.add.email": "E-posta", "users.add.phone": "Telefon", "users.add.password": "Şifre", "users.add.status": "Durum", "users.add.active": "Aktif", "users.add.pasive": "<PERSON><PERSON><PERSON>", "users.add.addUser": "Kullanıcı Ekle", "users.add.editUser": "Kullanıcıyı Düzenle", "users.add.soundFilled": "<PERSON><PERSON>", "users.add.extensionNumberServiceError": "<PERSON>ir sorun olu<PERSON>. Lütfen daha sonra tekrar deneyin.", "users.add.save": "<PERSON><PERSON>", "users.add.extensionNumberFindError": "Bu numara daha önce kayıtlıdır", "users.add.passwordDesc.desc1": "En az bir küçük harf bulunmalıdır.", "users.add.passwordDesc.desc2": "En az bir büyük harf bulunmalıdır.", "users.add.passwordDesc.desc3": "En az bir rakam bulunmalıdır.", "users.add.passwordDesc.desc4": "Toplamda en az 6 karakter olmalıdır.", "users.add.smsPermission": "Sms İzni", "users.add.emailPermission": "E-posta İzni", "users.add.mobilePushPermission": "Mobil Push İzni", "users.add.webPushPermission": "Web Push İzni", "users.filter.filterData": "Verileri Filtrele", "users.filter.filterButton": "Filtrele", "users.import.importData": "<PERSON><PERSON>", "users.import.upload": "<PERSON><PERSON><PERSON>", "users.import.uploaderFileTitle": "Dosyayı Yükleyin veya Sürükleyin", "users.import.uploaderFileDesc": "Desteklenen dosya formatı: xlsx", "customers.customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customers.addButton": "<PERSON><PERSON>", "customers.deleteAllButton": "Sil", "customers.export": "Dışarıya Aktar", "customers.import": "içe aktarmak", "customers.filter": "filtre", "customers.searchPlaceholder": "Ara", "customers.convertToTempCustomerDesc": "Bu öğe müşteriye dönüşülecektir. Onaylıyor musunuz?", "customers.list.customer": "Müş<PERSON>i", "customers.list.customerPhone": "Müşteri Telefon Numarası", "customers.list.showAll": "Tümünü <PERSON>ö<PERSON>", "customers.list.deleteQuickFilterModalStatus": "Uyarı", "customers.list.deleteQuickFilterModalDesc": "<PERSON>u o<PERSON>e si<PERSON>. Onaylıyor musunuz?", "customers.list.deleteQuickFilterModalOK": "<PERSON><PERSON>", "customers.list.deleteQuickFilterModalCancel": "Vazgeç", "customers.list.type": "Tip", "customers.list.sector": "Se<PERSON><PERSON><PERSON>", "customers.list.phone": "Telefon", "customers.list.classification": "Sınıflandırma", "customers.list.email": "E-posta", "customers.list.extension": "<PERSON><PERSON><PERSON>", "customers.list.department": "<PERSON><PERSON><PERSON>", "customers.list.delete": "Sil", "customers.list.edit": "<PERSON><PERSON><PERSON><PERSON>", "customers.list.cancel": "Vazgeç", "customers.list.warning": "Uyarı", "customers.list.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "customers.add.hideInformation": "Bilgilerini Gizle", "customers.add.addressTitle": "<PERSON><PERSON> Başlığı", "customers.add.customerRepresentative": "Müşteri Temsilcileri", "customers.add.assignCustomerRepresentative": "Temsilciye Ata", "customers.add.general": "<PERSON><PERSON>", "customers.add.concat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customers.add.dataSucessfullayUploader": "Verileriniz başarıyla yüklendi", "customers.add.permissions": "<PERSON><PERSON><PERSON>", "customers.add.options": "Seçenekler", "customers.add.extension": "<PERSON><PERSON><PERSON>", "customers.add.noneCallStatus": "Bilinmiyor", "customers.add.talking": "Görüşülüyor", "customers.add.chats": "<PERSON><PERSON><PERSON><PERSON>", "customers.add.answered": "Cevaplandı", "customers.add.addNote": "Not Ekle", "customers.add.ended": "Sonlandırıldı", "customers.add.addManuallyAddress": "<PERSON>", "customers.add.addAddressWithGoogle": "Google ile Adres Ekle", "customers.add.classificationDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, müş<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> veya özelliklerine göre gruplamanızı sağlar", "customers.add.classificationDescExample": "Örn: VIP, Sık <PERSON>, <PERSON> Liste, Yüksek Potansiyelli gibi gruplar oluşturabilirsiniz", "customers.add.name": "Ad", "customers.add.surName": "Soyad", "customers.add.email": "E-posta", "customers.add.phone": "Telefon", "customers.add.customerKind": "Müşteri Türü", "customers.add.customer": "Müş<PERSON>i", "customers.add.potentialCustomer": "Potansi<PERSON>l <PERSON>üşteri", "customers.add.renew": "<PERSON><PERSON><PERSON>", "customers.add.active": "Aktif", "customers.add.passive": "<PERSON><PERSON><PERSON>", "customers.add.suspended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customers.add.customerSource": "Müşteri Kaynağı", "customers.add.customerStatus": "Müşteri Durumu", "customers.add.taxOffice": "<PERSON><PERSON><PERSON>", "customers.add.identification": "<PERSON><PERSON>", "customers.add.mainLanguage": "<PERSON>", "customers.add.availableLanguages": "<PERSON><PERSON><PERSON>", "customers.add.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "customers.add.individual": "<PERSON><PERSON><PERSON><PERSON>", "customers.add.corporate": "<PERSON><PERSON><PERSON>", "customers.add.companyName": "Firma Adı", "customers.add.topCompany": "Üst Firma", "customers.add.contact": "İletişim", "customers.add.title": "Başlık", "customers.add.languages": "<PERSON><PERSON>", "customers.add.save": "<PERSON><PERSON>", "customers.add.status": "Durum", "customers.add.segmentations": "Segmentasyonlar", "customers.add.addresses": "<PERSON><PERSON><PERSON>", "customers.add.country": "<PERSON><PERSON><PERSON>", "customers.add.state": "İl", "customers.add.city": "İlçe", "customers.add.neighborhood": "Mahalle", "customers.add.postCode": "Posta Kodu", "customers.add.redirection": "Yönlendirme", "customers.add.inform": "Bilgilendirme", "customers.add.taxNumber": "<PERSON><PERSON><PERSON>", "customers.add.identificationNumber": "<PERSON><PERSON>", "customers.add.mananger": "Yönetici", "customers.add.informationalEmails": "Bilgilendirme E-Postaları", "customers.add.call": "<PERSON><PERSON>", "customers.add.agent": "<PERSON><PERSON><PERSON><PERSON>", "customers.add.durations": "<PERSON><PERSON><PERSON><PERSON>", "customers.add.direction": "<PERSON><PERSON><PERSON>", "customers.add.outbound": "Giden", "customers.add.inbound": "<PERSON><PERSON><PERSON>", "customers.add.missed": "Cevapsız", "customers.add.address": "<PERSON><PERSON>", "customers.add.date": "<PERSON><PERSON><PERSON>", "customers.add.chat": "<PERSON><PERSON><PERSON>", "customers.add.channel": "<PERSON><PERSON>", "customers.add.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customers.add.notes": "Notlar", "customers.add.createdUser": "Oluşturan Kullanıcı", "customers.add.createdDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customers.add.password": "Şifre", "customers.add.profession": "Meslek", "customers.add.addCustomer": "Müşter<PERSON>", "customers.add.editCustomer": "Müşteriyi düzenle", "customers.add.extensionNumberFindError": "Bu numara daha önce kayıtlıdır", "customers.add.passwordDesc.desc1": "En az bir küçük harf bulunmalıdır.", "customers.add.passwordDesc.desc2": "En az bir büyük harf bulunmalıdır.", "customers.add.passwordDesc.desc3": "En az bir rakam bulunmalıdır.", "customers.add.passwordDesc.desc4": "Toplamda en az 6 karakter olmalıdır.", "customers.filter.filterData": "Verileri Filtrele", "customers.filter.filterButton": "Filtrele", "customers.filter.fullName": "Ad Soyad", "customers.filter.identificationOrTaxNumber": "Kimlik veya Vergi <PERSON>", "customers.filter.filterAddress": "<PERSON><PERSON><PERSON>", "customers.import.importData": "<PERSON><PERSON>", "customers.import.upload": "<PERSON><PERSON><PERSON>", "customers.import.uploaderFileTitle": "Dosyayı Yükleyin veya Sürükleyin", "customers.import.uploaderFileDesc": "Desteklenen dosya formatı: xlsx", "chat.chat": "<PERSON><PERSON><PERSON>", "chat.chats": "<PERSON><PERSON><PERSON><PERSON>", "chat.startChat": "<PERSON><PERSON><PERSON>", "chat.newConversation": "<PERSON><PERSON>", "chat.start": "<PERSON><PERSON><PERSON>", "chat.newConversationInputDesc": "<PERSON><PERSON><PERSON>, dahili numara veya e-posta yazın", "chat.next": "Sonra", "chat.previous": "Önce", "chat.countinue": "<PERSON><PERSON>", "chat.createGroupChat": "Grup Sohbeti Oluştur", "chat.archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chat.endChat": "Sohbeti Bitir", "chat.openTicket": "Destek <PERSON>bi Aç", "chat.block": "<PERSON><PERSON><PERSON>", "chat.cleanAllHistories": "<PERSON><PERSON>m Geçmişi Temizle", "chat.download": "<PERSON><PERSON><PERSON>", "chat.loading": "Yükleniyor...", "calls.calls": "Aramalar", "panel.panel": "Panel", "authority.authority-main-title": "<PERSON><PERSON><PERSON>", "authority.user_name": "Kullanıcı Adı", "authority.department": "<PERSON><PERSON><PERSON>", "authority.role": "Rol", "authority.email": "Email", "authority.status": "Durum", "authority.save": "<PERSON><PERSON>", "authority.selectAll": "Tümü Seç", "authority.removeSelectAll": "<PERSON><PERSON>m seçilileri kaldır", "authority.active": "Aktif", "authority.passive": "<PERSON><PERSON><PERSON>", "authority.edit": "<PERSON><PERSON><PERSON><PERSON>", "authority.addOrUpdateAuthDesc": "Bu kullanıcının erişimi için lütfen ilgili modülleri seçiniz", "authority.edit_authority": "Kullanıcı Yetkisini Düzenle", "authority.select_department_and_permissions": "Lütfen departman ve yetki seçiniz!", "authority.department_permissions_assigned": "De<PERSON><PERSON> yetkileri başarıyla atandı!", "authority.select_user_and_permissions": "Lütfen kullanıcı ve yetki seçiniz!", "authority.user_permissions_assigned": "Kullanıcı yetkileri başarıyla atandı!", "authority.add_permission": "<PERSON><PERSON>", "authority.role_permission": "Rol Yetkilendirme", "authority.department_permission": "<PERSON><PERSON><PERSON>", "authority.user_permission": "Kullanıcı Yetkilendirme", "authority.select_role": "Rol Seçimi", "authority.select_department": "<PERSON><PERSON><PERSON>", "authority.select_user": "Kullanıcı Seçimi", "authority.assign_permission": "<PERSON><PERSON>", "authority.topOptions.add": "<PERSON><PERSON>", "authority.topOptions.filter": "Filtrele", "authority.topOptions.add_permission": "<PERSON><PERSON>", "authority.detailsFilter-department": "<PERSON><PERSON><PERSON>", "authority.detailsFilter-user": "Kullanıcı", "authority.detailsFilter-permission_name": "Yetki Adı", "authority.detailsFilter-status": "Durum", "authority.detailsFilter-role": "Rol", "authority.detailsFilter-filter": "Filtrele", "form.transactionSuccessful": "İşlem Başarılı", "form.transactionFaild": "İşlem Başarısız", "pauses.pauses": "<PERSON><PERSON><PERSON>", "settings.settings": "<PERSON><PERSON><PERSON>", "settings.save": "<PERSON><PERSON>", "settings.newPassword": "<PERSON><PERSON>", "settings.oldPassword": "Eski Şifre", "settings.changePassword": "<PERSON><PERSON><PERSON>", "settings.change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.profile": "Profil", "settings.confirmNewPassword": "<PERSON><PERSON>", "settings.passwordsDoNotMatch": "<PERSON><PERSON><PERSON><PERSON>şmi<PERSON>r", "settings.passwordRequired": "<PERSON><PERSON><PERSON>", "settings.emailSettings": "E-Posta Ayarları", "settings.mailPort": "Mail Portu", "settings.mailServer": "Mail Sunucusu", "settings.mailUser": "Mail Kullanıcısı", "settings.mailPassword": "Mail Şifresi", "settings.mailSslEnabled": "Mail SSL Etkin", "settings.smsSettings": "SMS Ayarları", "settings.smsProvider": "SMS Sağlayıcı", "settings.smsCompanyCode": "SMS Şirket Kodu", "settings.smsUserName": "SMS Kullanıcı Adı", "settings.smsPassword": "SMS Şifre", "settings.smsPassword2": "SMS Şifre 2", "settings.smsUserBaslik": "SMS Başlık", "settings.smsFastLoginMessage": "Hızlı Giriş Mesajı", "settings.smsPhoneConfirmMessage": "Telefon Onay Mesajı", "settings.defaultPhonePrefix": "Varsayılan Telefon Kodu", "settings.whatsappSettings": "WhatsApp Ayarları", "settings.whatsappWebhookSecret": "WhatsApp Webhook Secret", "settings.whatsappApiUrl": "WhatsApp API URL", "settings.whatsappApiToken": "WhatsApp API Token", "settings.3cxSettings": "3CX Ayarları", "settings.useThreeCX": "3CX Kullanılsın mı", "settings.threeCXApiUrl": "3CX API URL", "settings.threeCXSecretKey": "3CX Secret Key", "settings.threeCXApiUsername": "3CX API Kullanıcı Adı", "settings.threeCXApiPassword": "3CX API Şifre", "settings.threeCXRecordingPath": "3CX Kayıt Path", "settings.generalSettings": "<PERSON><PERSON>", "settings.id": "Id", "settings.title": "Başlık", "settings.cacheTime": "Önbellek Süresi", "settings.defaultRegion": "Varsayılan Bölge", "settings.defaultLanguage": "Varsayılan Dil", "settings.mediaPath": "<PERSON><PERSON><PERSON>", "settings.ticketCodeFormat": "Ticket Kod Formatı", "settings.taskCodeFormat": "Görev Kod Formatı", "settings.defaultBaseChatId": "Varsayılan Numara Id", "settings.mobilePushAccountFilePath": "<PERSON><PERSON>", "validation.requiredField": "<PERSON><PERSON> <PERSON><PERSON>", "roles.roles": "Roller", "roles.add": "<PERSON><PERSON>", "roles.save": "<PERSON><PERSON>", "roles.edit": "<PERSON><PERSON><PERSON><PERSON>", "roles.delete": "Sil", "roles.name": "İsim", "roles.addRole": "<PERSON><PERSON>", "roles.editRole": "<PERSON><PERSON><PERSON>", "roles.cancel": "Vazgeç", "roles.warning": "Uyarı", "roles.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "fileManager.scanFile": "Belge Tara", "fileManager.scanFileDesc2": "Bir belge tarayınız", "fileManager.scan": "Tara<PERSON>", "fileManager.scanFileDesc": "Tarat duğmesini tıklayarak bir belgeyi taratın ve önizlemesinigörün", "fileManager.noDocScannedYet": "Henüz taranmış bir belge yok", "fileManager.fileManager": "<PERSON><PERSON><PERSON>", "fileManager.save": "kaydet", "fileManager.uploadFile": "<PERSON><PERSON><PERSON>", "fileManager.folders": "<PERSON><PERSON><PERSON><PERSON>", "fileManager.addFolder": "<PERSON><PERSON><PERSON><PERSON>", "fileManager.editFolder": "<PERSON><PERSON><PERSON><PERSON>", "fileManager.cancel": "Vazgeç", "fileManager.delete": "Sil", "fileManager.warning": "Uyarı", "fileManager.moveFiles": "<PERSON><PERSON><PERSON><PERSON>", "fileManager.insertUser": "<PERSON><PERSON><PERSON>", "fileManager.name": "İsim", "fileManager.document": "<PERSON><PERSON><PERSON>", "fileManager.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "fileManager.unSelectFileTitle": "<PERSON><PERSON>", "fileManager.unSelectFileDesc": "Dosyaları incelemek için  tıklayınız.", "fileManager.downloadFile": "<PERSON><PERSON><PERSON>", "fileManager.cardView": "<PERSON><PERSON>", "fileManager.listView": "Liste Görünümü", "fileManager.insertDate": "Oluşturma Tarihi", "fileManager.updateDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileManager.type": "Tip", "fileManager.fileSize": "<PERSON><PERSON>", "fileManager.editName": "<PERSON><PERSON><PERSON>", "fileManager.filesUploadedSuccessfully": "Dosyalar Başarıyla Yüklendi", "profession.professions": "Meslekler", "profession.add": "<PERSON><PERSON>", "profession.edit": "<PERSON><PERSON><PERSON><PERSON>", "profession.delete": "Sil", "profession.name": "İsim", "profession.addProfession": "Meslek Ekle", "profession.editProfession": "Mesleği <PERSON>le", "profession.cancel": "Vazgeç", "profession.warning": "Uyarı", "profession.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "template.passwordRest": "Şifre <PERSON>ırl<PERSON>", "template.flow": "Akış", "template.pause": "<PERSON><PERSON>", "template.language": "Dil", "template.templates": "Şablonlar", "template.add": "<PERSON><PERSON>", "template.edit": "<PERSON><PERSON><PERSON><PERSON>", "template.delete": "Sil", "template.resetTemplate": "Şablonu Sıfırla", "template.name": "İsim", "template.title": "Başlık", "template.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "template.platform": "Platform", "template.email": "E-posta", "template.sms": "SMS", "template.mobilePush": "<PERSON><PERSON>", "template.webPush": "Web Bildirimi", "template.addTemplate": "<PERSON><PERSON><PERSON>", "template.editTemplate": "Şablonu <PERSON>", "template.tagDesc": "Değişken etiketlerine tıklayarak kopyalayabilir ve düzenlemek istediğiniz metin alanlarına yapıştırabilirsiniz.", "template.availableVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "template.copied": "Kopyalandı", "template.failedCopy": "Kopyalama başarısız", "template.cancel": "Vazgeç", "template.warning": "Uyarı", "template.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "template.type": "Tip", "template.status": "Durum", "template.content": "İçerik", "customerSource.customerSources": "Müşteri Kaynakları", "customerSource.add": "<PERSON><PERSON>", "customerSource.edit": "<PERSON><PERSON><PERSON><PERSON>", "customerSource.delete": "Sil", "customerSource.name": "Ad", "customerSource.addCustomerSource": "Müşteri Kaynağı Ekle", "customerSource.editCustomerSource": "Müşteri Kaynağını Düzenle", "customerSource.cancel": "İptal", "customerSource.warning": "Uyarı", "customerSource.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "notificationWay.notificationWays": "<PERSON><PERSON><PERSON><PERSON>", "notificationWay.add": "<PERSON><PERSON>", "notificationWay.edit": "<PERSON><PERSON><PERSON><PERSON>", "notificationWay.delete": "Sil", "notificationWay.name": "İsim", "notificationWay.addNotificationWay": "<PERSON><PERSON><PERSON><PERSON>", "notificationWay.editNotificationWay": "<PERSON><PERSON><PERSON><PERSON>", "notificationWay.cancel": "Vazgeç", "notificationWay.warning": "Uyarı", "notificationWay.notificationMethod": "<PERSON><PERSON><PERSON><PERSON>", "notificationWay.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "sector.sectors": "Sektörler", "sector.add": "<PERSON><PERSON>", "sector.edit": "<PERSON><PERSON><PERSON><PERSON>", "sector.delete": "Sil", "sector.name": "İsim", "sector.addSectors": "<PERSON><PERSON><PERSON><PERSON>", "sector.editSectors": "Sektörü <PERSON>", "sector.cancel": "Vazgeç", "sector.warning": "Uyarı", "sector.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "auditLog.logs": "Loglar", "auditLog.functionName": "Fonksiyon Adı", "auditLog.module": "<PERSON><PERSON><PERSON><PERSON>", "auditLog.selectedCount": "Seçilen Sayı<PERSON>ı", "auditLog.exportedCount": "Dışa Aktarılan Sayısı", "subjectTicket.subjectTickets": "Çağrı Konuları", "subjectTicket.add": "<PERSON><PERSON>", "subjectTicket.edit": "<PERSON><PERSON><PERSON><PERSON>", "subjectTicket.delete": "Sil", "subjectTicket.name": "İsim", "subjectTicket.addSubjectTicket": "<PERSON><PERSON>", "subjectTicket.editSubjectTicket": "<PERSON><PERSON><PERSON>", "subjectTicket.cancel": "Vazgeç", "subjectTicket.warning": "Uyarı", "subjectTicket.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "classification.classifications": "Sınıflandırmalar", "classification.add": "<PERSON><PERSON>", "classification.edit": "<PERSON><PERSON><PERSON><PERSON>", "classification.delete": "Sil", "classification.name": "Sınıf / Etiket Adı", "classification.tag": "Etiket", "classification.addClassification": "Sınıflandırma <PERSON>", "classification.editClassification": "Sınıflandırmayı Düzenle", "classification.cancel": "Vazgeç", "classification.warning": "Uyarı", "classification.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "callNotification.list.warning": "Uyarı", "callNotification.list.ok": "<PERSON><PERSON>", "callNotification.list.cancel": "Vazgeç", "callNotification.list.screenWillBeTrunOff": "Ekran kapanacak, onaylıyor musunuz?", "callNotification.list.saveAndClose": "<PERSON><PERSON>", "callNotification.list.closeTakeNote": "<PERSON><PERSON>", "callNotification.list.openTakeNote": "Notu Aç", "callNotification.list.addCustomer": "Müşter<PERSON>", "callNotification.list.findCustomer": "Müşteri Bul", "callNotification.list.selectCustomer": "Müşteri Seç", "callNotification.list.customerRepresentative": "Müşteri Temsilcileri", "callNotification.list.closeCustomerInfoes": "Müşteri Bilgilerini Kapat", "callNotification.list.openCustomerInfoes": "Müşteri Bilgilerini Aç", "callNotification.list.individual": "<PERSON><PERSON><PERSON><PERSON>", "callNotification.list.corporate": "<PERSON><PERSON><PERSON>", "callNotification.list.callNote": "Çağrı Notu", "tempCustomer.list.tempCustomers": "Geçici Müşteriler", "tempCustomer.list.addButton": "<PERSON><PERSON>", "tempCustomer.list.deleteAllButton": "Tümünü <PERSON>", "tempCustomer.list.export": "Dışa Aktar", "tempCustomer.list.import": "İçe Aktar", "tempCustomer.list.filter": "Filtrele", "tempCustomer.list.searchPlaceholder": "Ara", "tempCustomer.filter.filterData": "Verileri Filtrele", "tempCustomer.filter.filterButton": "Filtrele", "tempCustomer.filter.fullName": "Ad Soyad", "tempCustomer.filter.identificationOrTaxNumber": "T.C. veya Vergi <PERSON>", "tempCustomer.filter.filterAddress": "<PERSON><PERSON><PERSON> Gö<PERSON>", "tempCustomer.import.importData": "Veri İçe Aktar", "tempCustomer.import.upload": "<PERSON><PERSON><PERSON>", "tempCustomer.import.uploaderFileTitle": "Dosyayı Yükleyin veya Sürükleyin", "tempCustomer.import.uploaderFileDesc": "Desteklenen dosya formatı: xlsx", "import.selectSheet": "<PERSON><PERSON>ç", "import.sourceName": "Kaynağın Ad<PERSON>", "import.excelColumns": "Excel Sütunları", "import.modelFields": "Model <PERSON>", "import.save": "<PERSON><PERSON>", "import.dataSourceName": "Veri Kaynağının Adı", "pause.list.breakRequests": "<PERSON><PERSON>", "pause.list.estimatedFinish": "<PERSON><PERSON><PERSON>", "pause.list.start": "Başlangıç", "pause.list.end": "Bitiş", "pause.list.finishBreakTime": " Molayı Bitir", "pause.list.counterTimerDesc": "Molanızın bitmesine kalan süre", "pause.list.addButton": "<PERSON><PERSON>", "pause.list.status": "Durum", "pause.list.ok": "<PERSON><PERSON>", "pause.list.cancel": "Vazgeç", "pause.list.breakExitTime": "<PERSON><PERSON>", "pause.list.sendRequest": "<PERSON><PERSON>", "pause.list.warning": "Uyarı", "pause.list.breakType": "<PERSON><PERSON>", "pause.list.awaitingApproval": "<PERSON><PERSON>", "pause.list.accepted": "Kabul Edilenler", "pause.list.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pause.list.started": "Başlayanlar", "pause.list.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pause.list.canceled": "İptal Edilenler", "pause.list.cancelRequest": "<PERSON><PERSON><PERSON>", "pause.list.startEndTime": "Başlangıç - Bitiş <PERSON>ı", "pause.list.seeAllStatuses": "<PERSON><PERSON><PERSON> Gör", "pause.list.staffName": "<PERSON>el Adı", "pause.list.date": "<PERSON><PERSON><PERSON>", "pause.list.descripion": "<PERSON><PERSON>ı<PERSON><PERSON>", "pause.list.endTime": "Bitiş <PERSON>üresi", "pause.list.startDate": "Başlangıç <PERSON>", "pause.list.startTime": "Başlangıç <PERSON>", "pause.list.allowedMin": "İzin Verilen Dakika", "pause.list.approvalStatus": "<PERSON><PERSON>", "pause.list.staffDesc": "Personel Açıklaması", "pause.list.officialStatement": "Yetkili <PERSON>ı<PERSON>ı", "pause.list.acceptButton": "Kabul Et", "pause.list.rejectButton": "<PERSON><PERSON>", "pause.list.updateBreakStatus": "<PERSON><PERSON>", "pauseType.pauseTypes": "<PERSON><PERSON>", "pauseType.add": "<PERSON><PERSON>", "pauseType.edit": "<PERSON><PERSON><PERSON><PERSON>", "pauseType.delete": "Sil", "pauseType.name": "Ad", "pauseType.allowedMin": "İzin Verilen Dakika", "pauseType.addPauseType": "<PERSON><PERSON>", "pauseType.editPauseType": "<PERSON><PERSON>", "pauseType.cancel": "İptal", "pauseType.warning": "Uyarı", "pauseType.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "language.languages": "<PERSON><PERSON>", "language.add": "<PERSON><PERSON>", "language.edit": "<PERSON><PERSON><PERSON><PERSON>", "language.delete": "Sil", "language.name": "İsim", "language.addLanguage": "<PERSON><PERSON>", "language.editLanguage": "<PERSON><PERSON>", "language.cancel": "Vazgeç", "language.warning": "Uyarı", "language.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "language.active": "Aktif", "language.passive": "<PERSON><PERSON><PERSON>", "language.status": "Durum", "language.code": "Kod", "department.selectedDepartment": "<PERSON><PERSON><PERSON><PERSON>", "department.departments": "Depar<PERSON><PERSON>", "department.add": "<PERSON><PERSON>", "department.edit": "<PERSON><PERSON><PERSON><PERSON>", "department.delete": "Sil", "department.topDepartment": "<PERSON><PERSON>", "department.name": "İsim", "department.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "department.addUsers": "Kullanıcı Ekle", "department.addDepartment": "<PERSON><PERSON><PERSON>", "department.editDepartment": "<PERSON><PERSON><PERSON>", "department.cancel": "Vazgeç", "department.warning": "Uyarı", "department.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "department.active": "Aktif", "department.passive": "<PERSON><PERSON><PERSON>", "department.status": "Durum", "department.code": "Kod", "department.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "ticket.list.activities": "Aktiviteler", "ticket.list.attachedFiles": "<PERSON><PERSON><PERSON>", "ticket.list.commentFiles": "<PERSON><PERSON>", "ticket.list.emptyFilesCommnet": "Yorum için dosya eklemek istiyorsanız + butonuna tıklayın", "ticket.list.addFile": "<PERSON><PERSON><PERSON>", "ticket.list.selectFile": "<PERSON><PERSON><PERSON>", "ticket.list.createTicketDesc": "nolu <PERSON><PERSON>z oluşturuldu", "ticket.list.savingData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ticket.list.updatingStatus": "Durum güncelleniyor", "ticket.list.loadingData": "<PERSON><PERSON><PERSON> yükleniyor", "ticket.list.address": "<PERSON><PERSON>", "ticket.list.addManualAddress": "<PERSON>", "ticket.list.selectLocationMap": "<PERSON><PERSON><PERSON>", "ticket.list.ticketFiles": "Ticket Dosyaları", "ticket.list.emptyTicketFiles": "Ticket dosyası eklemek için + butonuna tıklayın", "ticket.list.create": "Oluştur", "ticket.list.update": "<PERSON><PERSON><PERSON><PERSON>", "ticket.list.ticketType": "<PERSON>ick<PERSON>", "ticket.list.mainTicket": "<PERSON>et", "ticket.list.labels": "<PERSON><PERSON><PERSON><PERSON>", "ticket.list.selectedLocation": "Seçili <PERSON>", "ticket.list.loadMap": "<PERSON><PERSON>", "ticket.list.clean": "<PERSON><PERSON><PERSON>", "ticket.list.pin": "<PERSON>n", "ticket.list.type": "<PERSON><PERSON><PERSON>", "ticket.list.task": "<PERSON><PERSON><PERSON><PERSON>", "ticket.list.numberDaysPassed": "Geçen Gün <PERSON>ı", "ticket.list.showAll": "<PERSON><PERSON><PERSON>", "ticket.list.tickets": "<PERSON><PERSON><PERSON><PERSON>", "ticket.list.addSubTicket": "Alt Ticket Ekle", "ticket.list.subTickets": "<PERSON>", "ticket.list.explainActionYouPerformed": "Gerçekleştirdiğiniz işlemi açıklayınız", "ticket.list.subject": "<PERSON><PERSON>", "ticket.list.comments": "<PERSON><PERSON><PERSON>", "ticket.list.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "ticket.list.save": "<PERSON><PERSON>", "ticket.list.insertDate": "Oluşturma Tarihi", "ticket.list.ticket": "Ticket", "ticket.list.filesUploadedSuccessfully": "Dosyalar Başarıyla Yüklendi", "ticket.list.dateRange": "<PERSON><PERSON><PERSON>", "ticket.list.title": "Başlık", "ticket.list.customer": "Müş<PERSON>i", "ticket.list.departments": "Depar<PERSON><PERSON>", "ticket.list.email": "E-Posta", "ticket.list.all": "<PERSON><PERSON><PERSON>", "ticket.list.notificationWayNone": "<PERSON><PERSON>", "ticket.list.notificationWay": "<PERSON><PERSON><PERSON><PERSON>", "ticket.list.priority": "Öncelik", "ticket.list.endDate": "Bitiş Tarihi", "ticket.list.uploadFile": "<PERSON><PERSON><PERSON>", "ticket.list.sms": "SMS", "ticket.list.push": "<PERSON><PERSON>", "ticket.list.none": "Hiç<PERSON>i", "ticket.list.low": "Düşük", "ticket.list.atanan": "<PERSON><PERSON><PERSON>", "ticket.list.medium": "Orta", "ticket.list.high": "<PERSON><PERSON><PERSON><PERSON>", "ticket.list.critical": "<PERSON><PERSON><PERSON>", "ticket.list.addTicket": "Ticket <PERSON>", "ticket.list.editTicket": "<PERSON><PERSON><PERSON>", "ticket.list.statusName": "Durum", "ticket.list.watchers": "Ta<PERSON>p <PERSON>", "ticket.list.SubjectName": "<PERSON><PERSON>ı", "ticket.list.sslDate": "SLA (Gün)", "ticket.list.Code": "Kodu", "ticket.list.add": "Oluştur", "ticket.list.edit": "<PERSON><PERSON><PERSON><PERSON>", "ticket.list.delete": "Sil", "ticket.list.name": "İsim", "ticket.list.cancel": "Vazgeç", "ticket.list.warning": "Uyarı", "ticket.list.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "ticket.filter.filterData": "Verileri Filtrele", "ticket.filter.filterButton": "Filtrele", "task.list.status": "Durum", "task.list.reporterUser": "<PERSON>il<PERSON><PERSON>", "task.list.tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "task.list.assignedUser": "<PERSON><PERSON><PERSON>", "task.list.type": "<PERSON><PERSON><PERSON>", "task.list.task": "<PERSON><PERSON><PERSON><PERSON>", "task.list.dateRange": "<PERSON><PERSON><PERSON>", "task.list.title": "Başlık", "task.list.departments": "Depar<PERSON><PERSON>", "task.list.notificationWay": "<PERSON><PERSON><PERSON><PERSON>", "task.list.priority": "Öncelik", "task.list.endDate": "Bitiş Tarihi", "task.list.email": "E-posta", "task.list.sms": "SMS", "task.list.push": "<PERSON><PERSON>", "task.list.all": "<PERSON><PERSON><PERSON>", "task.list.none": "Hiç<PERSON>i", "task.list.low": "Düşük", "task.list.medium": "Orta", "task.list.high": "<PERSON><PERSON><PERSON><PERSON>", "task.list.critical": "<PERSON><PERSON><PERSON>", "task.list.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "task.list.addTask": "<PERSON><PERSON><PERSON><PERSON>", "task.list.editTask": "<PERSON><PERSON><PERSON><PERSON>", "task.list.add": "<PERSON><PERSON>", "task.list.edit": "<PERSON><PERSON><PERSON><PERSON>", "task.list.delete": "Sil", "task.list.name": "İsim", "task.list.cancel": "Vazgeç", "task.list.warning": "Uyarı", "task.list.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "task.filter.filterData": "Verileri Filtrele", "task.filter.filterButton": "Filtrele", "autoDialer.list.status": "Durum", "autoDialer.list.callNotes": "Çağrı Notları", "autoDialer.list.pending": "Beklemede", "autoDialer.list.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "autoDialer.list.incomingCall": "<PERSON><PERSON><PERSON>", "autoDialer.list.planed": "<PERSON><PERSON><PERSON>", "autoDialer.list.addToArchive": "<PERSON><PERSON><PERSON><PERSON>", "autoDialer.list.archive": "Arşiv", "autoDialer.list.youMustMatchFieldsYourFile": "Dosyanızdaki alanları eşleştirmeniz gerekir", "autoDialer.list.itemSelected": "<PERSON><PERSON><PERSON>", "autoDialer.list.inProgress": "<PERSON><PERSON>", "autoDialer.list.completed": "Tamamlandı", "autoDialer.list.canceled": "İptal Edildi", "autoDialer.list.numberDataAddedSystem": "Sisteme eklenmiş veri sayısı", "autoDialer.list.afterSucessImportTempCustoemrAutoDilerDesc": "Seç ve devam et tıklayarak otomatık arayıcınızı oluşturabilirsiniz", "autoDialer.list.autoDialers": "Otomatik <PERSON>", "autoDialer.list.save": "<PERSON><PERSON>", "autoDialer.list.dateRange": "<PERSON><PERSON><PERSON>", "autoDialer.list.title": "Başlık", "autoDialer.list.start": "Başla", "autoDialer.list.cancel": "İptal Et", "autoDialer.list.complete": "<PERSON><PERSON><PERSON>", "autoDialer.list.ok": "<PERSON><PERSON>", "autoDialer.list.numberSelectedCustomers": "Aranacak Müşteri Sayısı", "autoDialer.list.startStatusDesc": "Otomatik arayıcı başlancacaktır. Onaylıyor musunuz?", "autoDialer.list.numberSuccessfulUploads": "Başarılı yükleme sayısı", "autoDialer.list.numberitemGivingErrors": "Hata veren öğe sayı<PERSON>ı", "autoDialer.list.cancelStatusDesc": "Otomatik arayıcı iptal edilecektır. Onaylıyor musunuz?", "autoDialer.list.completeStatusDesc": "Otomatik arayıcı tamamlancaktır. Onaylıyor musunuz?", "autoDialer.list.archiveStatusDesc": "Otomatik arayıcı arşive eklenecektir. Onaylıyor musunuz?", "autoDialer.list.startDate": "Başlangıç <PERSON>", "autoDialer.list.queueNumber": "Kuyruk Numarası", "autoDialer.list.done": "<PERSON><PERSON><PERSON><PERSON>", "autoDialer.list.total": "Toplam", "autoDialer.list.ruleName": "<PERSON>ral Adı", "autoDialer.list.queue": "Kuyruk", "autoDialer.list.addCallingData": "<PERSON><PERSON>", "autoDialer.list.selectAndCountinue": "<PERSON><PERSON> ve <PERSON>", "autoDialer.list.importData": "<PERSON><PERSON>", "autoDialer.list.customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "autoDialer.list.externalData": "<PERSON><PERSON>", "autoDialer.list.importDataTitle": "Veri Aktarma Başlığı", "autoDialer.list.importDataDesc": "Veri aktarma açıklaması", "autoDialer.list.addAutoDialer": "Otomatik <PERSON>", "autoDialer.list.editAutoDialer": "Otomatik Arama <PERSON>", "autoDialer.list.add": "<PERSON><PERSON>", "autoDialer.list.edit": "<PERSON><PERSON><PERSON><PERSON>", "autoDialer.list.delete": "Sil", "autoDialer.list.name": "İsim", "autoDialer.list.warning": "Uyarı", "autoDialer.list.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "autoDialer.filter.filterData": "Verileri Filtrele", "autoDialer.filter.filterButton": "Filtrele", "quickfilter.deleteSuccess": "Hızlı filtre başarıyla silindi", "quickfilter.saveSuccess": "Hızlı filtre başarıyla kaydedildi", "quickfilter.filterLoading": "Hızlı filtreler yükleniyor...", "quickfilter.filterLoadingError": "Hızlı filtreler yüklenirken hata:", "quickfilter.filterSaveingError": "Hızlı filtre kaydedirlen hata oluştu", "quickfilter.filterDeletApprove": "Filtre Silme <PERSON>", "quickfilter.filterDeleteText": "<PERSON><PERSON><PERSON><PERSON>", "quickfilter.filterApproveText": "<PERSON><PERSON><PERSON>", "quickfilter.filterCancelText": "İptal", "quickfilter.delete": "Sil", "quickfilter.saveFilter": "Hızlı Filtre Kaydet", "quickfilter.saveText": "<PERSON><PERSON>", "quickfilter.filterName": "Filtre Adı", "quickfilter.filterNameRequiredError": "Hızlı filtre adı zorunludur", "threecxqueues.queues": "3CX Kuyruk", "threecxqueues.department": "<PERSON><PERSON><PERSON>", "threecxqueues.internalUsers": "<PERSON><PERSON><PERSON>", "threecxqueues.managers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threecxqueues.pollingStrategy": "Çağrı Dağıtım Stratejisi", "threecxqueues.masterTimeout": "<PERSON>ım<PERSON> (saniye)", "threecxqueues.ringTimeout": "<PERSON><PERSON> (saniye)", "threecxqueues.hunt": "S<PERSON><PERSON>la Çağır", "threecxqueues.ringAll": "Hepsini Aynı Anda Çağır", "threecxqueues.huntRandomStart": "Rastgele Başlayarak Sırayla Çağır", "threecxqueues.nextAgent": "<PERSON><PERSON><PERSON>", "threecxqueues.longestWaiting": "En Uzun Süredir <PERSON>", "threecxqueues.leastTalkTime": "<PERSON> Az <PERSON>", "threecxqueues.fewestAnswered": "En Az Çağrı Cevaplayan", "threecxqueues.huntBy3s": "Üçer Üçer Çağır", "threecxqueues.first3Available": "İlk Üç Müsait Temsilci", "threecxqueues.skillBasedRouting_RingAll": "Beceriye Göre - He<PERSON>ini Ara", "threecxqueues.skillBasedRouting_HuntRandomStart": "Beceriye Göre - Rastgele Başlayarak Ara", "threecxqueues.skillBasedRouting_RoundRobin": "Beceriye Göre - Sırayla <PERSON>", "threecxqueues.skillBasedRouting_FewestAnswered": "Beceriye Göre - En Az Cevaplayan", "threecxqueues.queueName": "Kuyruk Adı", "threecxqueues.queueNumber": "Kuyruk Numarası", "threecxqueues.queueAgents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threecxqueues.addQueu": "<PERSON><PERSON><PERSON>", "threecxqueues.updateQueu": "Kuyruk <PERSON>ü<PERSON>le", "threecxqueues.delQueu": "Kuyruk Sil", "threecxqueues.cancel": "İptal", "threecxqueues.delete": "Sil", "threecxqueues.deleteModalDesc": "Bu kuyruk silinecek. Onaylıyor musunuz?", "threecxqueues.warning": "Uyarı", "threecxqueues.edit": "<PERSON><PERSON><PERSON><PERSON>", "threecxqueues.add": "<PERSON><PERSON>", "notes.note": "Arama Notları", "notes.notes": "Notlar", "notes.callDetails": "Çağrı Detayları", "notes.callDetailsNoData": "Çağrı Detayları Bulunmadı", "notes.callId": "Çağrı Kimliği", "notes.customerInfoes": "Müşteri Bilgileri", "notes.noteTitle": "Arama Notları", "notes.callPhone": "<PERSON><PERSON><PERSON>", "notes.status": "Durum", "notes.noteDescription": "Arama Açıklaması", "notes.customerName": "Müş<PERSON>i", "notes.insertUser": "<PERSON><PERSON><PERSON>", "notes.insertDate": "<PERSON><PERSON><PERSON><PERSON>", "notes.customerId": "Müş<PERSON>i", "notes.insertUserId": "<PERSON><PERSON><PERSON>", "notes.dateRange": "<PERSON><PERSON><PERSON>", "notes.details": "Çağrı Notları", "workFlow.workFlow": "İş Akışı", "workFlow.workFlows": "İş Akışları", "workFlow.add": "<PERSON><PERSON>", "workFlow.edit": "<PERSON><PERSON><PERSON><PERSON>", "workFlow.delete": "Sil", "workFlow.name": "İsim", "workFlow.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "workFlow.addWorkFlow": "İş Akışı Ekle", "workFlow.editWorkFlow": "İş Akışını Düzenle", "workFlow.copyWorkFlow": "İş Akışını Kopyala", "workFlow.openEditor": "Editör<PERSON>", "workFlow.addStep": "<PERSON><PERSON><PERSON>", "workFlow.editStep": "Adımı Düzenle", "workFlow.fromStep": "Başlangıç <PERSON>ımı", "workFlow.toStep": "<PERSON><PERSON><PERSON>", "workFlow.deleteStep": "Adımı Sil", "workFlow.copy": "Kopyala", "workFlow.newName": "<PERSON><PERSON>", "workFlow.newDescription": "<PERSON><PERSON>", "workFlow.save": "<PERSON><PERSON>", "workFlow.addTransition": "Geçiş <PERSON>", "workFlow.editTransition": "Geçişi Düzenle", "workFlow.deleteTransition": "Geçişi Sil", "workFlow.addNode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "workFlow.editNode": "Düğümü Düzenle", "workFlow.deleteNode": "Düğümü Sil", "workFlow.type": "<PERSON><PERSON><PERSON>", "workFlow.process": "İşlem", "workFlow.start": "Başlangıç", "workFlow.end": "Bitiş", "workFlow.fromNode": "Başlangıç Düğümü", "workFlow.toNode": "<PERSON><PERSON><PERSON>", "workFlow.addRule": "<PERSON><PERSON>", "workFlow.editRule": "Kuralı Düzenle", "workFlow.back": "<PERSON><PERSON><PERSON>", "workFlow.tagCopied": "Etiket Kopyalandı", "workFlow.saveAndAddRule": "<PERSON><PERSON>", "workFlow.validation": "Doğrulama", "workFlow.action": "Aksiyon", "workFlow.tourStep1Title": "Her bir İş Adımı için durum ekleyin", "workFlow.tourStep1Desc": "İş Süreçleri otomize etmek için ekipler arası görev geçiş noktaları ekleyin", "workFlow.tourStep2Title": "İş Adımlarını Birbirine Bağlayın", "workFlow.tourStep2Desc": "Ekibinizin Adımlar Arasında Geçiş Kuralını Ayarlayın", "workFlow.tourStep3Title": "Adımlar ve bağlantılar", "workFlow.tourStep3Desc": "Oluşturduğunuz adımları ve aralarındaki bağlantıları toplu şekilde inceleyin.", "workFlow.tourStep4Title": "<PERSON><PERSON>örü<PERSON>ü<PERSON>", "workFlow.tourStep4Desc": "Akış diyagramınızdaki tüm adımları küçük bir harita üzerinden görün. Hızlıca konum değiştirin.", "workFlow.tourStep5Title": "Yakınlaştır / Uzaklaştır Kontrolleri", "workFlow.tourStep5Desc": "Diyagramı büyütmek veya küçültmek için bu kontrolleri kullanabilirsiniz. Gözlemi kolaylaştırır.", "workFlow.tourStep6Title": "Diyagram Önizlemesi", "workFlow.tourStep6Desc": "Sayfanın genel yapısını bu alanda görebilirsiniz. Önizleme, hangi adımın nerede olduğunu anlamanızı sağlar.", "workFlow.completeSelection": "<PERSON><PERSON><PERSON><PERSON>", "chat.name": "İsim", "chat.image": "Resim", "chat.video": "Video", "chat.document": "<PERSON><PERSON><PERSON>", "chat.fileSelected": "<PERSON><PERSON><PERSON>", "chat.totalSize": "Toplam Boyut", "chat.noFileSelectedYet": "<PERSON><PERSON><PERSON>z Do<PERSON>a <PERSON>çilmedi", "chat.location": "<PERSON><PERSON>", "chat.voice": "<PERSON><PERSON>", "chat.unsupportedType": "Desteklenmeyen Tip", "chat.sticker": "<PERSON>er", "chat.lastIncomingMessage": "<PERSON>", "chat.lastSentMessage": "<PERSON>", "chat.open": "Açık", "chat.closed": "<PERSON><PERSON><PERSON>", "chat.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "chat.close": "Ka<PERSON><PERSON>", "chat.closeChat": "<PERSON><PERSON><PERSON><PERSON>", "chat.archivedChat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chat.closeChatDesc": "Bu sohbet kapatılacak. Onaylıyor musunuz?", "chat.archiveChatDesc": "Bu sohbet arşivlenecek. Onaylıyor musunuz?", "chat.archivedChatDesc": "Bu sohbet arşivlenecek. Onaylıyor musunuz?", "chat.unSelectFileTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>", "chat.unSelectFileDesc": "Mesajlaşmaya başlamak için bir sohbet seçin.", "chat.createTicket": "Ticket <PERSON>", "chat.createAsCustomer": "Müşteri Olarak Oluştur", "chat.reply": "Yanıtla", "chat.unmute": "Sesi Aç", "chat.mute": "<PERSON><PERSON>", "chat.copy": "Kopyala", "chat.send": "<PERSON><PERSON><PERSON>", "chat.messageHasBeenCopied": "Mesaj k<PERSON>alandı", "chat.copyFaild": "Kopyalama Başarısız", "chat.writeYourMessage": "Mesajınızı yazın...", "chat.ok": "<PERSON><PERSON>", "chat.cancel": "İptal", "chat.youHaveNewMessage": "<PERSON><PERSON>j", "chat.clickToRead": "Okumak için tı<PERSON>ın", "chat.createChatTile": "<PERSON><PERSON>", "chat.create": "Oluştur", "chat.customer": "Müş<PERSON>i", "chat.createCustomerAndCreateTicket": "Müşteri Oluştur ve Ticket Ata", "chat.unsupportedFileType": "Desteklenmeyen Dosya <PERSON>", "chat.unsupportedFileTypeDesc": "<PERSON>u dosya türü önizleme için desteklenmiyor.", "chat.fileTypeNotSupported": "<PERSON>u dosya türü des<PERSON>klenmiyor", "chat.unknownFile": "Bilinmeyen <PERSON>a", "chat.downloadFile": "Dosyayı İndir", "chat.mapLoadError": "<PERSON><PERSON>", "chat.coordinates": "<PERSON><PERSON>inatl<PERSON>", "chat.openInMaps": "<PERSON><PERSON><PERSON>", "chat.viewLarge": "Büyük Görünüm", "chat.selectedLocation": "Seçili <PERSON>", "editor.noActionToUndo": "Geri alınacak işlem yok", "editor.noActionToRedo": "İleri alınacak işlem yok", "editor.redo": "İleri Al", "editor.undo": "<PERSON><PERSON>", "editor.startTypingWithVoice": "<PERSON><PERSON>", "editor.stopAudioRecording": "Ses Kaydı<PERSON>ı Durdur", "editor.bold": "Kalı<PERSON>", "editor.italic": "İtalik ", "editor.faces": "<PERSON><PERSON>z <PERSON>", "editor.hands": "Eller", "editor.hearts": "<PERSON><PERSON><PERSON>", "editor.objects": "<PERSON><PERSON><PERSON><PERSON>", "editor.Activities": "Aktiviteler", "editor.underLine": "Alt Çizgi", "editor.textColor": "<PERSON><PERSON>", "editor.heading1": "Başlık 1", "editor.heading2": "Başlık 2", "editor.heading3": "Başlık 3", "editor.alignLeft": "<PERSON><PERSON> Hizala", "editor.alignCenter": "<PERSON><PERSON><PERSON>", "editor.alignRight": "<PERSON><PERSON><PERSON>", "editor.bulletedList": "Madde İşaretli Liste", "editor.numberedList": "Numaralı Liste", "editor.addLink": "<PERSON>", "editor.linkText": "<PERSON>", "editor.checkLit": "<PERSON><PERSON><PERSON><PERSON>", "editor.url": "URL", "editor.cancel": "İptal", "editor.add": "<PERSON><PERSON>", "editor.uploadImage": "<PERSON><PERSON><PERSON>", "editor.uploadFromFileManagerTitle": "<PERSON><PERSON><PERSON>", "editor.uploadFromFileManagerDesc": "<PERSON><PERSON><PERSON><PERSON>", "editor.uploadFromComputerTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.uploadFromComputerDesc": "<PERSON><PERSON>", "editor.uploadByUrlTitle": "URL'den Ekle", "editor.uploadByUrlDesc": "Resim bağlantısı gir", "editor.uploadImageByURL": "Resim bağlantısı gir", "editor.imageUrl": "Resim URL", "editor.imageUrlInputDesc": "Geçerli bir resim URL'si girin (jpg, png, gif, webp desteklenir)", "editor.ok": "<PERSON><PERSON>", "editor.delete": "Sil", "editor.edit": "<PERSON><PERSON><PERSON><PERSON>", "editor.imageSettings": "<PERSON><PERSON><PERSON>", "editor.width": "Genişlik", "editor.height": "Yükseklik", "editor.alignment": "<PERSON><PERSON><PERSON>", "editor.left": "Sol", "editor.center": "Orta", "editor.right": "Sağ", "editor.alt": "<PERSON><PERSON><PERSON><PERSON>", "editor.caption": "Alt Başlık", "editor.save": "<PERSON><PERSON>", "editor.addEmoji": "<PERSON><PERSON><PERSON>", "editor.addTable": "<PERSON><PERSON><PERSON>", "editor.addRow": "<PERSON><PERSON><PERSON><PERSON>", "editor.addColumn": "<PERSON><PERSON><PERSON>", "editor.addHTMLCodeBlock": "HTML Kod Bloğu Ekle", "editor.infoPanel": "<PERSON>ilgi <PERSON>", "editor.infoPanelDesc": "Bilgileri renkli bir panelde vurgulayın", "editor.infoPanelDefaultDesc": "Açıklamayı Girin...", "editor.quote": "Alıntı", "editor.quoteDesc": "Bir alıntı veya alıntı ekleyin", "editor.divider": "<PERSON><PERSON><PERSON>", "editor.dividerDesc": "İçeriği yatay bir çizgiyle a<PERSON>ı<PERSON>ın", "editor.checkListItemDesct": "Açıklamayı Girin...", "editor.quoteDefaultDesc": "Açıklamayı Girin...", "editor.addMentions": "<PERSON><PERSON><PERSON><PERSON>", "editor.slateEditorPlaceholder": "Açıklamayı Girin...", "editor.user": "Kullanıcı", "editor.selectUser": "Kullanıcı Seçin", "editor.searchingFor": "Aranıyor...", "calendar.calendar": "Takvim", "calendar.noEventsFound": "Etkinlik bulunamadi", "calendar.list": "Liste", "calendar.calendarNotes": "Takvi<PERSON>", "calendar.add": "<PERSON><PERSON>", "calendar.edit": "<PERSON><PERSON><PERSON><PERSON>", "calendar.delete": "Sil", "calendar.addCalendarNote": "<PERSON><PERSON><PERSON><PERSON>", "calendar.editCalendarNote": "<PERSON><PERSON><PERSON><PERSON>", "calendar.name": "İsim", "calendar.addNote": "Not Ekle", "calendar.editNote": "Not Düzenle", "calendar.cancel": "Vazgeç", "calendar.warning": "Uyarı", "calendar.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "calendar.attendingUsers": "<PERSON><PERSON><PERSON>", "calendar.title": "Başlık", "calendar.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "calendar.startDateTime": "Başlangıç Tarihi ve <PERSON>ati", "calendar.endDateTime": "Bitiş Tarihi ve <PERSON>", "calendar.isRecurring": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calendar.isAllDay": "<PERSON><PERSON><PERSON>", "calendar.assignUser": "Kullanıcı Ata", "calendar.noteType": "Not Türü", "calendar.visibilities": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calendar.recurrenceType": "Tekrarlama Tipi", "calendar.reminderChannel": "Hatırlatma Kanalı", "calendar.relatedCustomer": "İlgili <PERSON>şteri", "calendar.assignedUser": "<PERSON><PERSON><PERSON>", "calendar.departments": "Depar<PERSON><PERSON>", "calendar.attendUsers": "Katılacak Kullanıcılar", "calendar.tags": "<PERSON><PERSON><PERSON><PERSON>", "calendar.importanceStatus": "<PERSON>ne<PERSON>", "calendar.isImportant": "<PERSON><PERSON><PERSON><PERSON>", "calendar.unimportant": "Önemsiz", "calendar.rangeDateTime": "<PERSON><PERSON><PERSON>", "calendar.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calendar.isPublicDesc": "Herkese Açık Açıklama", "calendar.note": "Not", "calendar.task": "<PERSON><PERSON><PERSON><PERSON>", "calendar.meeting": "Toplantı", "calendar.callAuto": "Otomatik <PERSON>", "calendar.personal": "<PERSON><PERSON><PERSON><PERSON>", "calendar.departmental": "<PERSON><PERSON><PERSON>", "calendar.public": "<PERSON><PERSON>", "calendar.email": "E-posta", "calendar.push": "<PERSON><PERSON><PERSON><PERSON>", "calendar.sms": "SMS", "calendar.pending": "Beklemede", "calendar.accepted": "Kabul Edildi", "calendar.declined": "Reddedildi", "calendar.daily": "Günlük", "calendar.weekly": "Haftalık", "calendar.monthly": "Aylık", "calendar.yearly": "Yıllık", "resource.resources": "<PERSON><PERSON>arı", "resource.add": "<PERSON><PERSON>", "resource.edit": "<PERSON><PERSON><PERSON><PERSON>", "resource.delete": "Sil", "resource.name": "İsim", "resource.addResource": "Dil kaynağı Ekle", "resource.editResource": "Dil kaynağı Düzenle", "resource.cancel": "Vazgeç", "resource.warning": "Uyarı", "resource.deleteModalDesc": "Bu öğe silinecek. Onaylıyor musunuz?", "resource.key": "<PERSON><PERSON><PERSON>", "resource.value": "<PERSON><PERSON><PERSON>", "resource.en": "İnglizce", "resource.tr": "Türkçe", "resource.language": "Dil", "resource.enValue": "İnglizce Karşılığı", "resource.trValue": "Türkçe Karşılığı", "notification.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notification.readed": "Okunanlar", "notification.unReaded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notification.title": "Başlık", "notification.message": "<PERSON><PERSON>", "notification.user": "Kullanıcı", "notification.date": "<PERSON><PERSON><PERSON>", "notification.markAllNotificationsRead": "<PERSON><PERSON><PERSON> bildirimleri okundu olarak işaretle", "notification.markAllMyNotificationRead": "Bana ait tüm bildirimleri okundu olarak işaretle", "notification.approveChangeAllNotificationStatus": "<PERSON>ü<PERSON> bildiri<PERSON>in du<PERSON>, onaylıyor musunuz?", "error.unknownError": "Bilinmeyen bir hata o<PERSON>", "error.notAuthorized": "Bu işlem için <PERSON>z bulunmamaktadır", "error.errorDuringProcess": "İşlem sırasında bir hata oluştu", "error.faildProcessError": "Hata mesajı işlenemedi", "error.validationEmailType": "E-Posta Geçersiz", "team.team": "Takım", "welcome": "Hoşgeldiniz", "filterAlreadyExists": "Filtre zaten mevcut", "login": "<PERSON><PERSON><PERSON>", "invalidFilterData": "Geçersiz Filtre Verisi", "clearFilterButton": "<PERSON><PERSON><PERSON><PERSON>", "saveQuickFilter": "Hızlı filtre kaydet", "deleteQuickFilterModalStatus": "Uyarı", "deleteQuickFilterModalDesc": "<PERSON>u o<PERSON>e si<PERSON>. Onaylıyor musunuz?", "deleteQuickFilterModalOK": "<PERSON><PERSON>", "deleteQuickFilterModalCancel": "Vazgeç", "today": "<PERSON><PERSON><PERSON><PERSON>", "previousDay": "<PERSON><PERSON><PERSON>", "atusageProcessError": "Bu öğe başka işlemler tarafından kullanılıyor, silinemez", "generalSearch": "Ara...", "showAll": "Tümünü <PERSON>ö<PERSON>"}