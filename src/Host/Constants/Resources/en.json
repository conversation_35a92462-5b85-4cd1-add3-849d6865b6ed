{"DefaultRole": "General", "FileUpload": "File Upload", "FileUploadSuccess": "File uploaded successfully.", "InvalidFileFormat": "Invalid file format.", "MissingRequiredField": "Required field is missing.", "UnauthorizedAccess": "You are not authorized to perform this action.", "FileNotFound": "File not found.", "FileTooLarge": "File size is too large.", "InvalidFileType": "Invalid file type.", "UploadFailed": "File upload failed.", "FileAlreadyExists": "File already exists.", "FileUploadInProgress": "File upload in progress.", "FileUploadCanceled": "File upload canceled.", "FileUploadCompleted": "File upload completed.", "FileUploadFailed": "File upload failed.", "FileUploadPaused": "File upload paused.", "FileUploadResumed": "File upload resumed.", "FileUploadRestarted": "File upload restarted.", "FileUploadAborted": "File upload aborted.", "FileUploadTimeout": "File upload timed out.", "FileUploadError": "File upload error.", "FileUploadNotSupported": "File upload not supported.", "FileUploadNotAllowed": "File upload not allowed.", "FileUploadLimitExceeded": "File upload limit exceeded.", "FileUploadInvalid": "File upload invalid.", "FileUploadSuccessful": "File upload successful.", "FileExtensionNotSupported": "Only {{Extensions}} extensions are supported.", "FileSizeExceeded": "Uploaded file cannot exceed {{MaxMb}}MB.", "Users.Status.Active": "Active", "Users.Status.Inactive": "Inactive", "User.NotFound": "User not found.", "ExcelUserColumns.Name": "Name", "ExcelUserColumns.Surname": "Surname", "ExcelUserColumns.Email": "Email", "ExcelUserColumns.Phone": "Phone", "ExcelUserColumns.Company": "Company", "ExcelUserColumns.Position": "Position", "ExcelUserColumns.Address": "Address", "ExcelUserColumns.City": "City", "ExcelUserColumns.ExtensionNo": "Extension No", "ExcelUserColumns.Departman": "Department", "ExcelUserColumns.Group": "Group", "ExcelUserColumns.Active": "Status(0:Inactive, 1:Active)", "ExcelUserColumns.Password": "Password", "ExcelUserColumns.InsertDate": "Insert Date", "ExcelReadError": "Data could not be read from the Excel file.", "InvalidExcelData": "The Excel file does not contain data matching the model.", "NoNewUsersToImport": "No new users to import.", "Row": "Row", "UnexpectedError": "An unexpected error occurred.", "NameRequired": "Name field cannot be empty.", "SurnameRequired": "Surname field cannot be empty.", "EmailRequired": "Email field cannot be empty.", "EmailFormatInvalid": "Please enter a valid email address.", "PhoneRequired": "Phone field cannot be empty.", "ExtentionNoRequired": "Extension number field cannot be empty.", "ExtentionNoFormatInvalid": "Extension number must consist of digits only.", "ExcelMappingError": "Row {{Row}}: The value '{{Value}}' in the '{{Column}}' column could not be mapped to the '{{Property}}' field.", "Import.ValidationError": "Row {{Row}: {{Detail}}", "Import.AlreadyExists": "Row {{Row}}: The email '{{Email}}' already exists in the system.", "Import.ExtensionAlreadyExists": "Row {{Row}}: The extension is '{{Extension}}' already exists in the system.", "Import.ExtensionAlreadyPhoneExists": "Row {{Row}}: '{{<PERSON><PERSON>}}', '{{Phone}}' already exists in the system.", "Import.PropertyValidationError": "Row {{Row}}: Field '{{Property}}' is invalid - {{Detail}}", "UserImport.ValidationFailed": "User import failed. Please ensure valid data is provided and try again.", "UserImport.AlreadyExists": "Row {{Row}}: The email '{{Email}}' already exists in the system.", "UserImport.ExtensionAlreadyExists": "Row {{Row}}: The extension '{{Extension}}' already exists in the system.", "UserImport.ValidationError": "Row {{Row}}: The following error occurred - '{{Detail}}'", "ExcelCustomerColumns.Name": "Name", "ExcelCustomerColumns.Surname": "Surname", "ExcelCustomerColumns.Email": "Email", "ExcelCustomerColumns.Phone": "Phone", "ExcelCustomerColumns.PhonePrefix": "Phone Prefix", "ExcelUserColumns.PhonePrefix": "Phone Prefix", "ExcelCustomerColumns.Type": "Customer Type: Individual, Corporate", "ExcelCustomerColumns.Kind": "Customer Type: Customer, PotentialCustomer, Renew", "ExcelCustomerColumns.Status": "Status: Active, Inactive, Suspended", "ExcelCustomerColumns.Advisor": "Advisor", "ExcelCustomerColumns.Classification": "Classification", "ExcelCustomerColumns.Source": "Source", "ExcelCustomerColumns.Country": "Country", "ExcelCustomerColumns.Address": "Address", "ExcelCustomerColumns.State": "State", "ExcelCustomerColumns.City": "City", "ExcelCustomerColumns.Province": "Province", "ExcelCustomerColumns.PostCode": "Post Code", "ExcelCustomerColumns.TaxNumber": "Tax Number", "ExcelCustomerColumns.TaxOffice": "Tax Office", "ExcelCustomerColumns.IdentificationNumber": "Identification Number", "ExcelCustomerColumns.Language": "Language", "ExcelCustomerColumns.AvailableLanguage": "Available Language", "ExcelCustomerColumns.Description": "Description", "ExcelCustomerColumns.MailBcc": "Mail BCC", "ExcelCustomerColumns.CustomerSource": "Customer Source", "ExcelCustomerColumns.NotificationWay": "Notification Way", "ExcelCustomerDto.NameEmpty": "Name field cannot be empty.", "ExcelCustomerDto.NameMaxLength": "Name field cannot exceed 100 characters.", "ExcelCustomerDto.SurnameEmpty": "Surname field cannot be empty.", "ExcelCustomerDto.SurnameMaxLength": "Surname field cannot exceed 100 characters.", "ExcelCustomerDto.EmailEmpty": "Email field cannot be empty.", "ExcelCustomerDto.EmailInvalid": "Invalid email format.", "ExcelCustomerDto.PhoneEmpty": "Phone field cannot be empty.", "ExcelCustomerDto.PhoneInvalid": "Invalid phone number format.", "ExcelCustomerDto.TypeEmpty": "Type field cannot be empty.", "ExcelCustomerDto.TypeInvalid": "Invalid type. Valid types: Individual, Corporate.", "ExcelCustomerDto.StatusInvalid": "Invalid status. Valid statuses: Active, Inactive, Suspended.", "ExcelCustomerDto.KindInvalid": "Invalid kind. Valid kinds: Customer, PotentialCustomer, Renew.", "ExcelCustomerDto.CountryMaxLength": "Country name cannot exceed 100 characters.", "ExcelCustomerDto.LanguageMaxLength": "Language name cannot exceed 50 characters.", "ExcelCustomerDto.DescriptionMaxLength": "Description cannot exceed 500 characters.", "ExcelCustomerDto.TaxOfficeMaxLength": "Tax office name cannot exceed 100 characters.", "ExcelCustomerDto.TaxNumberMaxLength": "Tax number cannot exceed 50 characters.", "ExcelCustomerDto.IdentificationNumberMaxLength": "Identification number cannot exceed 50 characters.", "ExcelCustomerDto.MailBccMaxLength": "BCC field cannot exceed 100 characters.", "ExcelCustomerDto.ClassificationMaxLength": "Classification name cannot exceed 100 characters.", "ExcelCustomerDto.CustomerSourceMaxLength": "Customer source cannot exceed 100 characters.", "ExcelCustomerDto.AdvisorEmailInvalid": "Invalid advisor email address.", "ExcelCustomerDto.NotificationWayMaxLength": "Notification way cannot exceed 100 characters.", "CustomerImport.ValidationFailed": "Customer import failed. Please ensure valid data is provided and try again.", "CustomerImport.AlreadyExists": "Row {{Row}}: The email '{{Email}}' already exists in the system.", "CustomerImport.ExtensionAlreadyPhoneExists": "Row {{Row}}: '{{<PERSON><PERSON>}}', '{{Phone}}' already exists in the system.", "CustomerImport.AdvisorNotFound": "Row {{Row}}: No advisor found with the email address '{{AdvisorEmail}}'.", "CustomerImport.ValidationFailedDetail": "Please correct the errors below and try again.", "Customers.NotFound": "Customer not found.", "TempCustomer.Validation.NameSurnameRequired": "Name and surname are required.", "TempCustomer.Validation.EmailInvalid": "Invalid email format: {Email}", "TempCustomer.Validation.PhoneInvalid": "Phone number must consist of digits only.", "Export.LimitExceeded": "A maximum of 10,000 records can be exported.", "Export.Success": "Export operation was successful.", "EmailPart.EmptyFallback": "_empty", "UnknownUser": "Unknown User", "Customers.BulkDeleteCustomer.NoIdsProvided": "No customer IDs were provided for deletion.", "Customers.BulkDeleteCustomer.Failed": "Customer deletion failed. Please ensure valid customer IDs are provided and try again.", "Customers.BulkDeleteCustomer.Success": "Customer deletion successful.", "Customers.BulkDeleteCustomer.SomeIdsNotFound": "Some customer IDs provided for deletion were not found.", "Customers.DeleteCustomer.Failed": "Customer deletion failed. Please ensure a valid customer ID is provided and try again.", "Customers.DeleteTempCustomer.Failed": "Temporary customer deletion failed. Please ensure a valid customer ID is provided and try again.", "IdMismatch": "The provided ID does not match the expected ID.", "FilterNotFound": "Filter not found.", "UserNotFound": "User not found.", "FilterAlreadyExists": "Filter with name '{Name}' already exists.", "Calendar.StartTimeWasInThePast": "Start time cannot be in the past.", "Calendar.EndTimeWasBeforeStartTime": "End time cannot be before start time.", "Calendar.InvalidNoteType": "Invalid note type.", "Calendar.InvalidVisibility": "Invalid visibility type.", "Calendar.InvalidEventType": "Invalid event type.", "Calendar.RequiredCustomer": "Customer selection is required.", "Calendar.Validation.MissingCustomerForAutoCall": "Customer selection is required for auto call type.", "Calendar.Validation.DepartmentRequired": "Department must be selected.", "Calendar.Validation.StartDateCannotBePast": "Start date cannot be in the past.", "Calendar.Validation.EndDateBeforeStart": "End date cannot be before start date.", "Calendar.Validation.EndDateMustBeAfterStartDate": "End date must be after start date.", "Calendar.TagAlreadyExists": "A tag with the name '{Name}' already exists.", "Calendar.UnknownDepartment": "Unknown Department", "Calendar.UnknownUser": "Unknown User", "Calendar.Note.NotFound": "The specified calendar note was not found.", "Calendar.Note.Unauthorized": "You do not have permission to perform this action on this calendar note.", "Calendar.Note.DeleteSuccess": "Calendar note deleted successfully.", "Calendar.Note.UpdateSuccess": "Calendar note updated successfully.", "Calendar.Note.CreateSuccess": "Calendar note created successfully.", "Calendar.Note.ListSuccess": "Calendar notes retrieved successfully.", "Calendar.Note.ValidationFailed": "Calendar note validation failed.", "Calendar.Note.IdRequired": "Calendar note ID is required.", "Calendar.Note.TitleRequired": "Title is required.", "Calendar.Note.TitleMaxLength": "Title cannot be longer than 200 characters.", "Calendar.Note.DescriptionMaxLength": "Description cannot be longer than 2000 characters.", "Calendar.Note.StartDateRequired": "Start date is required.", "Calendar.Note.InvalidNoteType": "Invalid note type.", "Calendar.Note.InvalidVisibility": "Invalid visibility option.", "Calendar.Note.DepartmentRequiredForDepartmental": "Department selection is required for departmental visibility.", "Calendar.Note.ReminderMinutesInvalid": "Reminder minutes must be greater than or equal to 0.", "Calendar.Note.ReminderMinutesMaxLimit": "Reminder minutes cannot exceed 43200 (30 days).", "Calendar.Note.InvalidReminderChannel": "Invalid reminder channel.", "Calendar.Note.InvalidRecurrenceType": "Invalid recurrence type.", "Calendar.Note.RecurrenceIntervalInvalid": "Recurrence interval must be greater than 0.", "Calendar.Note.RecurrenceIntervalMaxLimit": "Recurrence interval cannot exceed 365.", "Calendar.Note.RecurrenceCountInvalid": "Recurrence count must be greater than 0.", "Calendar.Note.RecurrenceEndDateInvalid": "Recurrence end date must be in the future.", "Calendar.Note.PageNumberInvalid": "Page number must be greater than 0.", "Calendar.Note.PageSizeInvalid": "Page size must be greater than 0.", "Calendar.Note.PageSizeMaxLimit": "Page size must be less than or equal to 100.", "Calendar.Note.SortDirectionInvalid": "Sort direction must be 'asc' or 'desc'.", "Calendar.Note.DateRangeInvalid": "Start date must be less than or equal to end date.", "Calendar.TagNotFound": "The specified tag was not found.", "Calendar.TagInUse": "This tag cannot be deleted because it is in use.", "Calendar.Note.CompanyNotFound": "The specified company was not found.", "Calendar.Tag.NameRequired": "Tag name cannot be empty.", "Calendar.Tag.NameMaxLength": "Tag name cannot be longer than 50 characters.", "Calendar.Tag.NameUnique": "A tag with this name already exists.", "Calendar.Tag.IdRequired": "Tag ID cannot be empty.", "Calendar.Tag.NotFound": "The specified tag was not found.", "Calendar.Tag.InUse": "This tag cannot be deleted because it is in use.", "dashboard.topUsersByTaskCount": "Users by Task Count", "dashboard.dashboard": "Dashboard", "dashboard.statusOfTickets": "Status of Tickets", "dashboard.priorityStatusTasks": "Task Priority Status", "dashboard.unassigned": "Unassigned", "dashboard.searchAssignmentGraph": "Search Assignment Graph", "dashboard.unAssignedDesc": "The task has not been assigned to anyone yet", "dashboard.countinue": "In Progress", "dashboard.countinueDesc": "The task is currently in progress", "dashboard.low": "Low", "dashboard.medium": "Medium", "dashboard.high": "High", "dashboard.critical": "Critical", "dashboard.done": "Completed", "dashboard.doneDesc": "The task is completed", "dashboard.urgent": "<PERSON><PERSON>", "dashboard.urgentDesc": "The task is completed", "recording.recordings": "Recordings", "recording.callerName": "Caller Name", "recording.all": "All", "recording.caller": "Caller", "recording.callee": "<PERSON><PERSON>", "recording.calleePhoneNumber": "Callee Phone Number", "recording.callerPhoneNumber": "Caller Phone Number", "recording.calleeName": "Callee Name", "recording.direction": "Direction", "recording.inbound": "Inbound", "recording.outbound": "Outbound", "recording.startTime": "Start Time", "recording.endTime": "End Time", "recording.saveQuickFilter": "Save Quick Filter", "recording.answeredTime": "Answered Time", "recording.talkDurationInSeconds": "Talk Duration (seconds)", "recording.totalDurationInSeconds": "Total Duration (seconds)", "recording.copyDownloadLink": "Copy Download Link", "recording.isAnswered": "Answered?", "recording.extension": "Extension", "recording.transcription": "Transcription", "recording.export": "Export", "recording.delete": "Delete", "recording.missed": "Missed", "recording.answerd": "Answered", "recording.callStatus": "Call Status", "recording.showSummaryCall": "Show Call Summary", "recording.summary": "Summary", "recording.interviewSummary": "Interview Summary", "recording.fullContent": "Full Content", "recording.download": "Download", "recording.invalidLink": "Invalid Link", "recording.pathNotFound": "No Valid Recording Found", "recording.callDetails": "Call Details", "recording.sourceParticipantName": "Source Participant Name", "recording.sourceParticipantPhoneNumber": "Source Participant Phone Number", "general.pageNotFound": "Page Not Found", "general.gotoToDashboard": "Go to Dashboard", "general.forbiddenDesc": "You do not have permission to access this page", "header.takeBreak": "Take a Break", "header.myBreaks": "My Breaks", "header.avaliable": "Available", "header.away": "Away", "header.doNotDistrub": "Do Not Disturb", "header.lunch": "Lunch", "header.businessTrip": "Business Trip", "header.logout": "Logout", "header.settings": "Settings", "account.login": "<PERSON><PERSON>", "account.inCorrectEmailOrPass": "Your email or password is incorrect.", "account.email": "Email", "account.password": "Password", "account.forgotPassword": "Forgot Password", "account.sendEmail": "Send Email", "account.emailSent": "<PERSON><PERSON>", "account.changePassword": "Change Password", "account.newPassword": "New Password", "adminSidebar.notification&Tasks": "Notifications and Tasks", "adminSidebar.users": "Users", "adminSidebar.constant": "Constants", "adminSidebar.callReports": "Call Reports", "adminSidebar.calendarNotes": "Calendar Notes", "adminSidebar.fileManager": "File Manager", "adminSidebar.customers": "Customers", "adminSidebar.profession": "Profession", "adminSidebar.sectors": "Sectors", "adminSidebar.subjectTickets": "Subject Tickets", "adminSidebar.ticket": "Ticket", "adminSidebar.task": "Task", "adminSidebar.form": "Form", "adminSidebar.classification": "Classification", "adminSidebar.tag": "Tag", "adminSidebar.calendar": "Calendar", "adminSidebar.importData": "Import Data", "adminSidebar.autoDialer": "Auto Dialer", "adminSidebar.workflow": "Workflow", "adminSidebar.pauseManagement": "Break Management", "adminSidebar.department": "Department", "adminSidebar.recordings": "Recordings", "adminSidebar.authority": "Authority", "adminSidebar.roles": "Roles", "adminSidebar.report": "Report", "adminSidebar.languages": "Languages", "adminSidebar.customerSource": "Customer Source", "adminSidebar.langResources": "Language Resources", "leftMainSidebar.admin": "Admin", "users.users": "Users", "users.addButton": "Add", "users.deleteAllButton": "Delete", "users.export": "Export", "users.assignRole": "Assign Role", "users.searchPlaceholder": "Search", "users.save": "Save", "users.list.user": "User", "users.list.email": "Email", "users.list.extension": "Extension", "users.list.department": "Department", "users.list.delete": "Delete", "users.list.edit": "Edit", "users.list.cancel": "Cancel", "users.list.warning": "Warning", "users.list.deleteModalDesc": "This item will be deleted. Do you confirm?", "users.add.general": "General", "users.add.permissions": "Permissions", "users.add.options": "Options", "users.add.extension": "Extension", "users.add.name": "Name", "users.add.surName": "Surname", "users.add.email": "Email", "users.add.phone": "Phone", "users.add.password": "Password", "users.add.status": "Status", "users.add.active": "Active", "users.add.pasive": "Passive", "users.add.addUser": "Add User", "users.add.editUser": "Edit User", "users.add.soundFilled": "Mute", "users.add.extensionNumberServiceError": "An error occurred. Please try again later.", "users.add.save": "Save", "users.add.extensionNumberFindError": "This number is already registered", "users.add.passwordDesc.desc1": "At least one lowercase letter required.", "users.add.passwordDesc.desc2": "At least one uppercase letter required.", "users.add.passwordDesc.desc3": "At least one number required.", "users.add.passwordDesc.desc4": "At least 6 characters required.", "users.add.smsPermission": "SMS Permission", "users.add.emailPermission": "Email Permission", "users.add.mobilePushPermission": "Mobile Push Permission", "users.add.webPushPermission": "Web Push Permission", "users.filter.filterData": "Filter Data", "users.filter.filterButton": "Filter", "users.import.importData": "Import Data", "users.import.upload": "Upload", "users.import.uploaderFileTitle": "Upload or Drag File", "users.import.uploaderFileDesc": "Supported file format: xlsx", "customers.customers": "Customers", "customers.addButton": "Add", "customers.deleteAllButton": "Delete", "customers.export": "Export", "customers.import": "Import", "customers.filter": "Filter", "customers.searchPlaceholder": "Search", "customers.convertToTempCustomerDesc": "This item will be converted to a customer. Do you confirm?", "customers.list.customer": "Customer", "customers.list.customerPhone": "Customer Phone Number", "customers.list.showAll": "Show All", "customers.list.deleteQuickFilterModalStatus": "Warning", "customers.list.deleteQuickFilterModalDesc": "This item will be deleted. Do you confirm?", "customers.list.deleteQuickFilterModalOK": "OK", "customers.list.deleteQuickFilterModalCancel": "Cancel", "customers.list.type": "Type", "customers.list.sector": "Sector", "customers.list.phone": "Phone", "customers.list.classification": "Classification", "customers.list.email": "Email", "customers.list.extension": "Extension", "customers.list.department": "Department", "customers.list.delete": "Delete", "customers.list.edit": "Edit", "customers.list.cancel": "Cancel", "customers.list.warning": "Warning", "customers.list.deleteModalDesc": "This item will be deleted. Do you confirm?", "customers.add.hideInformation": "Hide Information", "customers.add.addressTitle": "Address Title", "customers.add.customerRepresentative": "Customer Representatives", "customers.add.assignCustomerRepresentative": "Assign to Representative", "customers.add.general": "General", "customers.add.concat": "Contact List", "customers.add.dataSucessfullayUploader": "Your data has been uploaded successfully", "customers.add.permissions": "Permissions", "customers.add.options": "Options", "customers.add.extension": "Extension", "customers.add.noneCallStatus": "Unknown", "customers.add.talking": "In Call", "customers.add.chats": "Chats", "customers.add.answered": "Answered", "customers.add.addNote": "Add Note", "customers.add.ended": "Ended", "customers.add.addManuallyAddress": "Add Address Manually", "customers.add.addAddressWithGoogle": "Add Address with Google", "customers.add.classificationDesc": "Classification allows you to group your customers based on behavior, value, or characteristics", "customers.add.classificationDescExample": "Example: You can create groups like <PERSON>, <PERSON><PERSON><PERSON> Complainer, <PERSON><PERSON>, High Potential", "customers.add.name": "Name", "customers.add.surName": "Surname", "customers.add.email": "Email", "customers.add.phone": "Phone", "customers.add.customerKind": "Customer Type", "customers.add.customer": "Customer", "customers.add.potentialCustomer": "Potential Customer", "customers.add.renew": "Recovered", "customers.add.active": "Active", "customers.add.passive": "Passive", "customers.add.suspended": "Suspended", "customers.add.customerSource": "Customer Source", "customers.add.customerStatus": "Customer Status", "customers.add.taxOffice": "Tax Office", "customers.add.identification": "Identification", "customers.add.mainLanguage": "Main Language", "customers.add.availableLanguages": "Available Languages", "customers.add.description": "Description", "customers.add.individual": "Individual", "customers.add.corporate": "Corporate", "customers.add.companyName": "Company Name", "customers.add.topCompany": "Parent Company", "customers.add.contact": "Contact", "customers.add.title": "Title", "customers.add.languages": "Languages", "customers.add.save": "Save", "customers.add.status": "Status", "customers.add.segmentations": "Segmentations", "customers.add.addresses": "Addresses", "customers.add.country": "Country", "customers.add.state": "State", "customers.add.city": "City", "customers.add.neighborhood": "Neighborhood", "customers.add.postCode": "Post Code", "customers.add.redirection": "Redirection", "customers.add.inform": "Inform", "customers.add.taxNumber": "Tax Number", "customers.add.identificationNumber": "Identification Number", "customers.add.mananger": "Manager", "customers.add.informationalEmails": "Informational Emails", "customers.add.call": "Call", "customers.add.agent": "Agent", "customers.add.durations": "Durations", "customers.add.direction": "Direction", "customers.add.outbound": "Outbound", "customers.add.inbound": "Inbound", "customers.add.missed": "Missed", "customers.add.address": "Address", "customers.add.date": "Date", "customers.add.chat": "Cha<PERSON>", "customers.add.channel": "Channel", "customers.add.users": "Users", "customers.add.notes": "Notes", "customers.add.createdUser": "Created By", "customers.add.createdDate": "Created Date", "customers.add.password": "Password", "customers.add.profession": "Profession", "customers.add.addCustomer": "Add Customer", "customers.add.editCustomer": "Edit Customer", "customers.add.extensionNumberFindError": "This number is already registered", "customers.add.passwordDesc.desc1": "At least one lowercase letter required.", "customers.add.passwordDesc.desc2": "At least one uppercase letter required.", "customers.add.passwordDesc.desc3": "At least one number required.", "customers.add.passwordDesc.desc4": "At least 6 characters required.", "customers.filter.filterData": "Filter Data", "customers.filter.filterButton": "Filter", "customers.filter.fullName": "Full Name", "customers.filter.identificationOrTaxNumber": "ID or Tax Number", "customers.filter.filterAddress": "Filter Address", "customers.import.importData": "Import Data", "customers.import.upload": "Upload", "customers.import.uploaderFileTitle": "Upload or Drag File", "customers.import.uploaderFileDesc": "Supported file format: xlsx", "chat.chat": "Cha<PERSON>", "chat.chats": "Chats", "chat.startChat": "Start Chat", "chat.newConversation": "New Chat", "chat.start": "Start", "chat.newConversationInputDesc": "Enter name, extension number or email", "chat.next": "Next", "chat.previous": "Previous", "chat.countinue": "Continue", "chat.createGroupChat": "Create Group Chat", "chat.archive": "Archive", "chat.endChat": "End <PERSON>", "chat.openTicket": "Open Support Ticket", "chat.block": "Block", "chat.cleanAllHistories": "Clear All Histories", "chat.download": "Download", "chat.loading": "Loading...", "calls.calls": "Calls", "panel.panel": "Panel", "authority.authority-main-title": "Authorities", "authority.user_name": "User Name", "authority.department": "Department", "authority.role": "Role", "authority.email": "Email", "authority.status": "Status", "authority.save": "Save", "authority.selectAll": "Select All", "authority.removeSelectAll": "Remove All Selected", "authority.active": "Active", "authority.passive": "Inactive", "authority.edit": "Edit", "authority.addOrUpdateAuthDesc": "Please select the relevant modules for this user's access", "authority.edit_authority": "Edit User Authority", "authority.select_department_and_permissions": "Please select department and permissions!", "authority.department_permissions_assigned": "Department permissions successfully assigned!", "authority.select_user_and_permissions": "Please select user and permissions!", "authority.user_permissions_assigned": "User permissions successfully assigned!", "authority.add_permission": "Add Permission", "authority.role_permission": "Role Authorization", "authority.department_permission": "Department Authorization", "authority.user_permission": "User Authorization", "authority.select_role": "Select Role", "authority.select_department": "Select Department", "authority.select_user": "Select User", "authority.assign_permission": "Assign Permission", "authority.topOptions.add": "Add", "authority.topOptions.filter": "Filter", "authority.topOptions.add_permission": "Add Permission", "authority.detailsFilter-department": "Department", "authority.detailsFilter-user": "User", "authority.detailsFilter-permission_name": "Permission Name", "authority.detailsFilter-status": "Status", "authority.detailsFilter-role": "Role", "authority.detailsFilter-filter": "Filter", "form.transactionSuccessful": "Transaction Successful", "form.transactionFaild": "Transaction Failed", "pauses.pauses": "Breaks", "settings.settings": "Settings", "settings.save": "Save", "settings.newPassword": "New Password", "settings.oldPassword": "Old Password", "settings.changePassword": "Change Password", "settings.change": "Change", "settings.profile": "Profile", "settings.confirmNewPassword": "Confirm New Password", "settings.passwordsDoNotMatch": "Passwords do not match", "settings.passwordRequired": "Password is required", "settings.emailSettings": "<PERSON><PERSON>s", "settings.mailPort": "Mail Port", "settings.mailServer": "Mail Server", "settings.mailUser": "Mail User", "settings.mailPassword": "Mail Password", "settings.mailSslEnabled": "Mail SSL Enabled", "settings.smsSettings": "SMS Settings", "settings.smsProvider": "SMS Provider", "settings.smsCompanyCode": "SMS Company Code", "settings.smsUserName": "SMS Username", "settings.smsPassword": "SMS Password", "settings.smsPassword2": "SMS Password 2", "settings.smsUserBaslik": "SMS Title", "settings.smsFastLoginMessage": "Fast Login Message", "settings.smsPhoneConfirmMessage": "Phone Confirmation Message", "settings.defaultPhonePrefix": "Default Phone Prefix", "settings.whatsappSettings": "WhatsA<PERSON> Settings", "settings.whatsappWebhookSecret": "WhatsApp Webhook Secret", "settings.whatsappApiUrl": "WhatsApp API URL", "settings.whatsappApiToken": "WhatsApp API Token", "settings.3cxSettings": "3CX Settings", "settings.useThreeCX": "Use 3CX", "settings.threeCXApiUrl": "3CX API URL", "settings.threeCXSecretKey": "3CX Secret Key", "settings.threeCXApiUsername": "3CX API Username", "settings.threeCXApiPassword": "3CX API Password", "settings.threeCXRecordingPath": "3CX Recording Path", "settings.generalSettings": "General Settings", "settings.id": "Id", "settings.title": "Title", "settings.cacheTime": "<PERSON><PERSON>", "settings.defaultRegion": "Default Region", "settings.defaultLanguage": "Default Language", "settings.mediaPath": "Media Path", "settings.ticketCodeFormat": "Ticket Code Format", "settings.taskCodeFormat": "Task Code Format", "settings.defaultBaseChatId": "Default Number Id", "settings.mobilePushAccountFilePath": "Mobile Push Account File Path", "validation.requiredField": "This field is required", "roles.roles": "Roles", "roles.add": "Add", "roles.save": "Save", "roles.edit": "Edit", "roles.delete": "Delete", "roles.name": "Name", "roles.addRole": "Add Role", "roles.editRole": "Edit Role", "roles.cancel": "Cancel", "roles.warning": "Warning", "roles.deleteModalDesc": "This item will be deleted. Do you confirm?", "fileManager.scanFile": "Scan Document", "fileManager.scanFileDesc2": "Please scan a document", "fileManager.scan": "<PERSON><PERSON>", "fileManager.scanFileDesc": "Click the scan button to scan a document and see its preview", "fileManager.noDocScannedYet": "No document scanned yet", "fileManager.fileManager": "File Manager", "fileManager.save": "Save", "fileManager.uploadFile": "Upload", "fileManager.folders": "Folders", "fileManager.addFolder": "Add Folder", "fileManager.editFolder": "Edit <PERSON>", "fileManager.cancel": "Cancel", "fileManager.delete": "Delete", "fileManager.warning": "Warning", "fileManager.moveFiles": "Move", "fileManager.insertUser": "Added By User", "fileManager.name": "Name", "fileManager.document": "File", "fileManager.deleteModalDesc": "This item will be deleted. Do you confirm?", "fileManager.unSelectFileTitle": "Select a Folder", "fileManager.unSelectFileDesc": "Click to view files.", "fileManager.downloadFile": "Download", "fileManager.cardView": "Card View", "fileManager.listView": "List View", "fileManager.insertDate": "Creation Date", "fileManager.updateDate": "Update Date", "fileManager.type": "Type", "fileManager.fileSize": "Size", "fileManager.editName": "Edit Name", "fileManager.filesUploadedSuccessfully": "Files Uploaded Successfully", "profession.professions": "Professions", "profession.add": "Add", "profession.edit": "Edit", "profession.delete": "Delete", "profession.name": "Name", "profession.addProfession": "Add Profession", "profession.editProfession": "Edit Profession", "profession.cancel": "Cancel", "profession.warning": "Warning", "profession.deleteModalDesc": "This item will be deleted. Do you confirm?", "template.passwordRest": "Password Reset", "template.flow": "Flow", "template.pause": "Break", "template.language": "Language", "template.templates": "Templates", "template.add": "Add", "template.edit": "Edit", "template.delete": "Delete", "template.resetTemplate": "Reset Template", "template.name": "Name", "template.title": "Title", "template.description": "Description", "template.platform": "Platform", "template.email": "Email", "template.sms": "SMS", "template.mobilePush": "Mobile Push", "template.webPush": "Web Push", "template.addTemplate": "Add Template", "template.editTemplate": "Edit Template", "template.tagDesc": "Click on variable tags to copy and paste them into the text fields you want to edit.", "template.availableVariables": "Available Variables", "template.copied": "<PERSON>pied", "template.failedCopy": "<PERSON><PERSON> Failed", "template.cancel": "Cancel", "template.warning": "Warning", "template.deleteModalDesc": "This item will be deleted. Do you confirm?", "template.type": "Type", "template.status": "Status", "template.content": "Content", "customerSource.customerSources": "Customer Sources", "customerSource.add": "Add", "customerSource.edit": "Edit", "customerSource.delete": "Delete", "customerSource.name": "Name", "customerSource.addCustomerSource": "Add Customer Source", "customerSource.editCustomerSource": "Edit Customer Source", "customerSource.cancel": "Cancel", "customerSource.warning": "Warning", "customerSource.deleteModalDesc": "This item will be deleted. Do you confirm?", "notificationWay.notificationWays": "Notification Methods", "notificationWay.add": "Add", "notificationWay.edit": "Edit", "notificationWay.delete": "Delete", "notificationWay.name": "Name", "notificationWay.addNotificationWay": "Add Notification Method", "notificationWay.editNotificationWay": "Edit Notification Method", "notificationWay.cancel": "Cancel", "notificationWay.warning": "Warning", "notificationWay.notificationMethod": "Notification Method", "notificationWay.deleteModalDesc": "This item will be deleted. Do you confirm?", "sector.sectors": "Sectors", "sector.add": "Add", "sector.edit": "Edit", "sector.delete": "Delete", "sector.name": "Name", "sector.addSectors": "Add Sector", "sector.editSectors": "Edit Sector", "sector.cancel": "Cancel", "sector.warning": "Warning", "sector.deleteModalDesc": "This item will be deleted. Do you confirm?", "auditLog.logs": "Logs", "auditLog.functionName": "Function Name", "auditLog.module": "<PERSON><PERSON><PERSON>", "auditLog.selectedCount": "Selected Count", "auditLog.exportedCount": "Exported Count", "subjectTicket.subjectTickets": "Call Subjects", "subjectTicket.add": "Add", "subjectTicket.edit": "Edit", "subjectTicket.delete": "Delete", "subjectTicket.name": "Name", "subjectTicket.addSubjectTicket": "Add Subject", "subjectTicket.editSubjectTicket": "Edit Subject", "subjectTicket.cancel": "Cancel", "subjectTicket.warning": "Warning", "subjectTicket.deleteModalDesc": "This item will be deleted. Do you confirm?", "classification.classifications": "Classifications", "classification.add": "Add", "classification.edit": "Edit", "classification.delete": "Delete", "classification.name": "Class / Tag Name", "classification.tag": "Tag", "classification.addClassification": "Add Classification", "classification.editClassification": "Edit Classification", "classification.cancel": "Cancel", "classification.warning": "Warning", "classification.deleteModalDesc": "This item will be deleted. Do you confirm?", "callNotification.list.warning": "Warning", "callNotification.list.ok": "OK", "callNotification.list.cancel": "Cancel", "callNotification.list.screenWillBeTrunOff": "Screen will turn off, do you confirm?", "callNotification.list.saveAndClose": "Save and Close", "callNotification.list.closeTakeNote": "Close Note", "callNotification.list.openTakeNote": "Open Note", "callNotification.list.addCustomer": "Add Customer", "callNotification.list.findCustomer": "Find Customer", "callNotification.list.selectCustomer": "Select Customer", "callNotification.list.customerRepresentative": "Customer Representatives", "callNotification.list.closeCustomerInfoes": "Close Customer Info", "callNotification.list.openCustomerInfoes": "Open Customer Info", "callNotification.list.individual": "Individual", "callNotification.list.corporate": "Corporate", "callNotification.list.callNote": "Call Note", "tempCustomer.list.tempCustomers": "Temporary Customers", "tempCustomer.list.addButton": "Add", "tempCustomer.list.deleteAllButton": "Delete All", "tempCustomer.list.export": "Export", "tempCustomer.list.import": "Import", "tempCustomer.list.filter": "Filter", "tempCustomer.list.searchPlaceholder": "Search", "tempCustomer.filter.filterData": "Filter Data", "tempCustomer.filter.filterButton": "Filter", "tempCustomer.filter.fullName": "Full Name", "tempCustomer.filter.identificationOrTaxNumber": "T.C. or Tax Number", "tempCustomer.filter.filterAddress": "Filter by Address", "tempCustomer.import.importData": "Import Data", "tempCustomer.import.upload": "Upload", "tempCustomer.import.uploaderFileTitle": "Upload or Drag File", "tempCustomer.import.uploaderFileDesc": "Supported file format: xlsx", "import.selectSheet": "Select Sheet", "import.sourceName": "Source Name", "import.excelColumns": "Excel Columns", "import.modelFields": "Model Fields", "import.save": "Save", "import.dataSourceName": "Data Source Name", "pause.list.breakRequests": "Break Requests", "pause.list.estimatedFinish": "Estimated Finish", "pause.list.start": "Start", "pause.list.end": "End", "pause.list.finishBreakTime": "Finish Break", "pause.list.counterTimerDesc": "Time remaining until your break ends", "pause.list.addButton": "Add", "pause.list.status": "Status", "pause.list.ok": "OK", "pause.list.cancel": "Cancel", "pause.list.breakExitTime": "Break Exit Time", "pause.list.sendRequest": "Send Request", "pause.list.warning": "Warning", "pause.list.breakType": "Break Type", "pause.list.awaitingApproval": "Awaiting <PERSON><PERSON><PERSON><PERSON>", "pause.list.accepted": "Accepted", "pause.list.rejected": "Rejected", "pause.list.started": "Started", "pause.list.completed": "Completed", "pause.list.canceled": "Canceled", "pause.list.cancelRequest": "Cancel Request", "pause.list.startEndTime": "Start - End Time", "pause.list.seeAllStatuses": "See All Statuses", "pause.list.staffName": "Staff Name", "pause.list.date": "Date", "pause.list.descripion": "Description", "pause.list.endTime": "End Time", "pause.list.startDate": "Start Date", "pause.list.startTime": "Start Time", "pause.list.allowedMin": "Allowed Minutes", "pause.list.approvalStatus": "Approval Status", "pause.list.staffDesc": "Staff Description", "pause.list.officialStatement": "Official Statement", "pause.list.acceptButton": "Accept", "pause.list.rejectButton": "Reject", "pause.list.updateBreakStatus": "Update Break Status", "pauseType.pauseTypes": "Break Types", "pauseType.add": "Add", "pauseType.edit": "Edit", "pauseType.delete": "Delete", "pauseType.name": "Name", "pauseType.allowedMin": "Allowed Minutes", "pauseType.addPauseType": "Add Break Type", "pauseType.editPauseType": "Edit Break Type", "pauseType.cancel": "Cancel", "pauseType.warning": "Warning", "pauseType.deleteModalDesc": "This item will be deleted. Do you confirm?", "language.languages": "Languages", "language.add": "Add", "language.edit": "Edit", "language.delete": "Delete", "language.name": "Name", "language.addLanguage": "Add Language", "language.editLanguage": "Edit Language", "language.cancel": "Cancel", "language.warning": "Warning", "language.deleteModalDesc": "This item will be deleted. Do you confirm?", "language.active": "Active", "language.passive": "Passive", "language.status": "Status", "language.code": "Code", "department.selectedDepartment": "Selected Department", "department.departments": "Departments", "department.add": "Add", "department.edit": "Edit", "department.delete": "Delete", "department.topDepartment": "Top Department", "department.name": "Name", "department.users": "Users", "department.addUsers": "Add User", "department.addDepartment": "Add Department", "department.editDepartment": "Edit Department", "department.cancel": "Cancel", "department.warning": "Warning", "department.deleteModalDesc": "This item will be deleted. Do you confirm?", "department.active": "Active", "department.passive": "Passive", "department.status": "Status", "department.code": "Code", "department.description": "Description", "ticket.list.activities": "Activities", "ticket.list.attachedFiles": "Attached Files", "ticket.list.commentFiles": "Comment Files", "ticket.list.emptyFilesCommnet": "Click the + button to add a file for comments", "ticket.list.addFile": "Add File", "ticket.list.selectFile": "Select File", "ticket.list.createTicketDesc": "Your ticket with number has been created", "ticket.list.savingData": "Saving", "ticket.list.updatingStatus": "Updating Status", "ticket.list.loadingData": "Loading Data", "ticket.list.address": "Address", "ticket.list.addManualAddress": "Add Manual Address", "ticket.list.selectLocationMap": "Select Location from Map", "ticket.list.ticketFiles": "Ticket Files", "ticket.list.emptyTicketFiles": "Click + button to add ticket files", "ticket.list.create": "Create", "ticket.list.update": "Update", "ticket.list.ticketType": "Ticket Type", "ticket.list.mainTicket": "Main Ticket", "ticket.list.labels": "Labels", "ticket.list.selectedLocation": "Selected Location", "ticket.list.loadMap": "Loading Map", "ticket.list.clean": "Clear", "ticket.list.pin": "<PERSON>n", "ticket.list.type": "Type", "ticket.list.task": "Task", "ticket.list.numberDaysPassed": "Number of Days Passed", "ticket.list.showAll": "Show All", "ticket.list.tickets": "Tickets", "ticket.list.addSubTicket": "Add Sub Ticket", "ticket.list.subTickets": "Sub Tickets", "ticket.list.explainActionYouPerformed": "Explain the action you performed", "ticket.list.subject": "Subject", "ticket.list.comments": "Comments", "ticket.list.description": "Description", "ticket.list.save": "Save", "ticket.list.insertDate": "Creation Date", "ticket.list.ticket": "Ticket", "ticket.list.filesUploadedSuccessfully": "Files Uploaded Successfully", "ticket.list.dateRange": "Date Range", "ticket.list.title": "Title", "ticket.list.customer": "Customer", "ticket.list.departments": "Departments", "ticket.list.email": "Email", "ticket.list.all": "All", "ticket.list.notificationWayNone": "None", "ticket.list.notificationWay": "Notification Preference", "ticket.list.priority": "Priority", "ticket.list.endDate": "End Date", "ticket.list.uploadFile": "Upload File", "ticket.list.sms": "SMS", "ticket.list.push": "Push Notification", "ticket.list.none": "None", "ticket.list.low": "Low", "ticket.list.atanan": "Assigned To", "ticket.list.medium": "Medium", "ticket.list.high": "High", "ticket.list.critical": "Critical", "ticket.list.addTicket": "Create Ticket", "ticket.list.editTicket": "Edit Ticket", "ticket.list.statusName": "Status", "ticket.list.watchers": "Watchers", "ticket.list.SubjectName": "Subject Name", "ticket.list.sslDate": "SLA (Days)", "ticket.list.Code": "Code", "ticket.list.add": "Create", "ticket.list.edit": "Edit", "ticket.list.delete": "Delete", "ticket.list.name": "Name", "ticket.list.cancel": "Cancel", "ticket.list.warning": "Warning", "ticket.list.deleteModalDesc": "This item will be deleted. Do you confirm?", "ticket.filter.filterData": "Filter Data", "ticket.filter.filterButton": "Filter", "task.list.status": "Status", "task.list.reporterUser": "Reporter", "task.list.tasks": "Tasks", "task.list.assignedUser": "Assigned User", "task.list.type": "Type", "task.list.task": "Task", "task.list.dateRange": "Date Range", "task.list.title": "Title", "task.list.departments": "Departments", "task.list.notificationWay": "Notification Preference", "task.list.priority": "Priority", "task.list.endDate": "End Date", "task.list.email": "Email", "task.list.sms": "SMS", "task.list.push": "Push Notification", "task.list.all": "All", "task.list.none": "None", "task.list.low": "Low", "task.list.medium": "Medium", "task.list.high": "High", "task.list.critical": "Critical", "task.list.description": "Description", "task.list.addTask": "Add Task", "task.list.editTask": "Edit Task", "task.list.add": "Add", "task.list.edit": "Edit", "task.list.delete": "Delete", "task.list.name": "Name", "task.list.cancel": "Cancel", "task.list.warning": "Warning", "task.list.deleteModalDesc": "This item will be deleted. Do you confirm?", "task.filter.filterData": "Filter Data", "task.filter.filterButton": "Filter", "autoDialer.list.status": "Status", "autoDialer.list.callNotes": "Call Notes", "autoDialer.list.pending": "Pending", "autoDialer.list.description": "Description", "autoDialer.list.incomingCall": "Incoming Call", "autoDialer.list.planed": "Planned", "autoDialer.list.addToArchive": "Add to Archive", "autoDialer.list.archive": "Archive", "autoDialer.list.youMustMatchFieldsYourFile": "You must match fields in your file", "autoDialer.list.itemSelected": "<PERSON><PERSON> selected", "autoDialer.list.inProgress": "In Progress", "autoDialer.list.completed": "Completed", "autoDialer.list.canceled": "Canceled", "autoDialer.list.numberDataAddedSystem": "Number of data added to the system", "autoDialer.list.afterSucessImportTempCustoemrAutoDilerDesc": "Click Select and Continue to create your auto dialer", "autoDialer.list.autoDialers": "Auto Dialers", "autoDialer.list.save": "Save", "autoDialer.list.dateRange": "Date Range", "autoDialer.list.title": "Title", "autoDialer.list.start": "Start", "autoDialer.list.cancel": "Cancel", "autoDialer.list.complete": "Complete", "autoDialer.list.ok": "OK", "autoDialer.list.numberSelectedCustomers": "Number of Customers to Call", "autoDialer.list.startStatusDesc": "The auto dialer will start. Do you confirm?", "autoDialer.list.numberSuccessfulUploads": "Number of successful uploads", "autoDialer.list.numberitemGivingErrors": "Number of items giving errors", "autoDialer.list.cancelStatusDesc": "The auto dialer will be canceled. Do you confirm?", "autoDialer.list.completeStatusDesc": "The auto dialer will be completed. Do you confirm?", "autoDialer.list.archiveStatusDesc": "The auto dialer will be added to archive. Do you confirm?", "autoDialer.list.startDate": "Start Date", "autoDialer.list.queueNumber": "Queue Number", "autoDialer.list.done": "Done", "autoDialer.list.total": "Total", "autoDialer.list.ruleName": "Rule Name", "autoDialer.list.queue": "Queue", "autoDialer.list.addCallingData": "Add Calling Data", "autoDialer.list.selectAndCountinue": "Select and Continue", "autoDialer.list.importData": "Import Data", "autoDialer.list.customers": "Customers", "autoDialer.list.externalData": "External Data", "autoDialer.list.importDataTitle": "Import Data Title", "autoDialer.list.importDataDesc": "Import Data Description", "autoDialer.list.addAutoDialer": "Add Auto Dialer", "autoDialer.list.editAutoDialer": "Edit Auto Dialer", "autoDialer.list.add": "Add", "autoDialer.list.edit": "Edit", "autoDialer.list.delete": "Delete", "autoDialer.list.name": "Name", "autoDialer.list.warning": "Warning", "autoDialer.list.deleteModalDesc": "This item will be deleted. Do you confirm?", "autoDialer.filter.filterData": "Filter Data", "autoDialer.filter.filterButton": "Filter", "quickfilter.deleteSuccess": "Quick filter deleted successfully", "quickfilter.saveSuccess": "Quick filter saved successfully", "quickfilter.filterLoading": "Loading quick filters...", "quickfilter.filterLoadingError": "Error loading quick filters:", "quickfilter.filterSaveingError": "Error occurred while saving quick filter", "quickfilter.filterDeletApprove": "Filter Delete Approval", "quickfilter.filterDeleteText": "Delete Filter", "quickfilter.filterApproveText": "Approve", "quickfilter.filterCancelText": "Cancel", "quickfilter.delete": "Delete", "quickfilter.saveFilter": "Save Quick Filter", "quickfilter.saveText": "Save", "quickfilter.filterName": "Filter Name", "quickfilter.filterNameRequiredError": "Quick filter name is required", "threecxqueues.queues": "3CX Queues", "threecxqueues.department": "Department", "threecxqueues.internalUsers": "Internal Users", "threecxqueues.managers": "Managers", "threecxqueues.pollingStrategy": "Call Distribution Strategy", "threecxqueues.masterTimeout": "Master Timeout (seconds)", "threecxqueues.ringTimeout": "Ring Timeout (seconds)", "threecxqueues.hunt": "Hunt Sequentially", "threecxqueues.ringAll": "Ring All Simultaneously", "threecxqueues.huntRandomStart": "Hunt Starting Randomly", "threecxqueues.nextAgent": "Next Available Agent", "threecxqueues.longestWaiting": "Longest Waiting", "threecxqueues.leastTalkTime": "Least Talk Time", "threecxqueues.fewestAnswered": "Fewest Answered", "threecxqueues.huntBy3s": "Hunt By 3", "threecxqueues.first3Available": "First 3 Available Agents", "threecxqueues.skillBasedRouting_RingAll": "Skill Based - Ring All", "threecxqueues.skillBasedRouting_HuntRandomStart": "Skill Based - Hunt Random Start", "threecxqueues.skillBasedRouting_RoundRobin": "Skill Based - Round Robin", "threecxqueues.skillBasedRouting_FewestAnswered": "Skill Based - Fewest Answered", "threecxqueues.queueName": "Queue Name", "threecxqueues.queueNumber": "Queue Number", "threecxqueues.queueAgents": "Agents", "threecxqueues.addQueu": "Add <PERSON>", "threecxqueues.updateQueu": "Update Queue", "threecxqueues.delQueu": "Delete Queue", "threecxqueues.cancel": "Cancel", "threecxqueues.delete": "Delete", "threecxqueues.deleteModalDesc": "This queue will be deleted. Do you confirm?", "threecxqueues.warning": "Warning", "threecxqueues.edit": "Edit", "threecxqueues.add": "Add", "notes.note": "Call Note", "notes.notes": "Notes", "notes.callDetails": "Call Details", "notes.callDetailsNoData": "Call Details Not Found", "notes.callId": "Call ID", "notes.customerInfoes": "Customer Information", "notes.noteTitle": "Call Notes", "notes.callPhone": "Number", "notes.status": "Status", "notes.noteDescription": "Call Description", "notes.customerName": "Customer", "notes.insertUser": "Added By", "notes.insertDate": "Date Added", "notes.customerId": "Customer", "notes.insertUserId": "Added By", "notes.dateRange": "Date Range", "notes.details": "Call Notes", "workFlow.workFlow": "Workflow", "workFlow.workFlows": "Workflows", "workFlow.add": "Add", "workFlow.edit": "Edit", "workFlow.delete": "Delete", "workFlow.name": "Name", "workFlow.description": "Description", "workFlow.addWorkFlow": "Add Workflow", "workFlow.editWorkFlow": "Edit Workflow", "workFlow.copyWorkFlow": "Copy Workflow", "workFlow.openEditor": "Open Editor", "workFlow.addStep": "Add Step", "workFlow.editStep": "Edit Step", "workFlow.fromStep": "From Step", "workFlow.toStep": "To Step", "workFlow.deleteStep": "Delete Step", "workFlow.copy": "Copy", "workFlow.newName": "New Name", "workFlow.newDescription": "New Description", "workFlow.save": "Save", "workFlow.addTransition": "Add Transition", "workFlow.editTransition": "Edit Transition", "workFlow.deleteTransition": "Delete Transition", "workFlow.addNode": "Add Node", "workFlow.editNode": "<PERSON>", "workFlow.deleteNode": "Delete Node", "workFlow.type": "Type", "workFlow.process": "Process", "workFlow.start": "Start", "workFlow.end": "End", "workFlow.fromNode": "From Node", "workFlow.toNode": "To Node", "workFlow.addRule": "Add Rule", "workFlow.editRule": "Edit Rule", "workFlow.back": "Back", "workFlow.tagCopied": "Tag Copied", "workFlow.saveAndAddRule": "Save and Add Rule", "workFlow.validation": "Validation", "workFlow.action": "Action", "workFlow.tourStep1Title": "Add status for each workflow step", "workFlow.tourStep1Desc": "Add task handoff points between teams to optimize workflows", "workFlow.tourStep2Title": "Connect Workflow Steps", "workFlow.tourStep2Desc": "Set transition rules between steps for your team", "workFlow.tourStep3Title": "Steps and connections", "workFlow.tourStep3Desc": "Review created steps and their connections collectively", "workFlow.tourStep4Title": "Overall Map View", "workFlow.tourStep4Desc": "See all steps in the flow diagram on a small map and reposition quickly", "workFlow.tourStep5Title": "Zoom Controls", "workFlow.tourStep5Desc": "Use these controls to zoom in or out, making observation easier", "workFlow.tourStep6Title": "Diagram Preview", "workFlow.tourStep6Desc": "Preview the page layout to see which step is where", "workFlow.completeSelection": "Complete Selection", "chat.name": "Name", "chat.image": "Image", "chat.video": "Video", "chat.document": "File", "chat.fileSelected": "File Selected", "chat.totalSize": "Total Size", "chat.noFileSelectedYet": "No File Selected Yet", "chat.location": "Location", "chat.voice": "Voice", "chat.unsupportedType": "Unsupported Type", "chat.sticker": "<PERSON>er", "chat.lastIncomingMessage": "Last Incoming Message", "chat.lastSentMessage": "Last Sent Message", "chat.open": "Open", "chat.closed": "Closed", "chat.archived": "Archived", "chat.close": "Close", "chat.closeChat": "Close Chat", "chat.archivedChat": "Archived Chat", "chat.closeChatDesc": "This chat will be closed. Do you confirm?", "chat.archiveChatDesc": "This chat will be archived. Do you confirm?", "chat.archivedChatDesc": "This chat will be archived. Do you confirm?", "chat.unSelectFileTitle": "Start Messaging", "chat.unSelectFileDesc": "Select a chat to start messaging.", "chat.createTicket": "Create Ticket", "chat.createAsCustomer": "Create as Customer", "chat.reply": "Reply", "chat.unmute": "Unmute", "chat.mute": "Mute", "chat.copy": "Copy", "chat.send": "Send", "chat.messageHasBeenCopied": "Message has been copied", "chat.copyFaild": "<PERSON><PERSON> Failed", "chat.writeYourMessage": "Write your message...", "chat.ok": "OK", "chat.cancel": "Cancel", "chat.youHaveNewMessage": "You have a new message", "chat.clickToRead": "Click to read", "chat.createChatTile": "Start New Chat", "chat.create": "Create", "chat.customer": "Customer", "chat.createCustomerAndCreateTicket": "Create Customer and Assign Ticket", "chat.unsupportedFileType": "Unsupported File Type", "chat.unsupportedFileTypeDesc": "This file type is not supported for preview", "chat.fileTypeNotSupported": "This file type is not supported", "chat.unknownFile": "Unknown File", "chat.downloadFile": "Download File", "chat.mapLoadError": "Map could not be loaded", "chat.coordinates": "Coordinates", "chat.openInMaps": "Open in Maps", "chat.viewLarge": "View Large", "chat.selectedLocation": "Selected Location", "editor.noActionToUndo": "No action to undo", "editor.noActionToRedo": "No action to redo", "editor.redo": "Redo", "editor.undo": "Undo", "editor.startTypingWithVoice": "Start Typing with Voice", "editor.stopAudioRecording": "Stop Audio Recording", "editor.bold": "Bold", "editor.italic": "Italic", "editor.faces": "Faces", "editor.hands": "Hands", "editor.hearts": "Hearts", "editor.objects": "Objects", "editor.Activities": "Activities", "editor.underLine": "Underline", "editor.textColor": "Text Color", "editor.heading1": "Heading 1", "editor.heading2": "Heading 2", "editor.heading3": "Heading 3", "editor.alignLeft": "Align Left", "editor.alignCenter": "Align Center", "editor.alignRight": "Align Right", "editor.bulletedList": "Bulleted List", "editor.numberedList": "Numbered List", "editor.addLink": "Add Link", "editor.linkText": "Link Text", "editor.checkLit": "Checklist", "editor.url": "URL", "editor.cancel": "Cancel", "editor.add": "Add", "editor.uploadImage": "Upload Image", "editor.uploadFromFileManagerTitle": "Add from File Manager", "editor.uploadFromFileManagerDesc": "Select from server files", "editor.uploadFromComputerTitle": "Add from Computer", "editor.uploadFromComputerDesc": "Upload local file", "editor.uploadByUrlTitle": "Add from URL", "editor.uploadByUrlDesc": "Enter image URL", "editor.uploadImageByURL": "Enter Image URL", "editor.imageUrl": "Image URL", "editor.imageUrlInputDesc": "Enter a valid image URL (jpg, png, gif, webp supported)", "editor.ok": "OK", "editor.delete": "Delete", "editor.edit": "Edit", "editor.imageSettings": "Image Settings", "editor.width": "<PERSON><PERSON><PERSON>", "editor.height": "Height", "editor.alignment": "Alignment", "editor.left": "Left", "editor.center": "Center", "editor.right": "Right", "editor.alt": "Alt Text", "editor.caption": "Caption", "editor.save": "Save", "editor.addEmoji": "Add <PERSON>", "editor.addTable": "Add Table", "editor.addRow": "Add Row", "editor.addColumn": "Add Column", "editor.addHTMLCodeBlock": "Add HTML Code Block", "editor.infoPanel": "Info Panel", "editor.infoPanelDesc": "Highlight information in a colored panel", "editor.infoPanelDefaultDesc": "Enter description...", "editor.quote": "Quote", "editor.quoteDesc": "Add a quote or citation", "editor.divider": "Divider", "editor.dividerDesc": "Separate content with a horizontal line", "editor.checkListItemDesct": "Enter description...", "editor.quoteDefaultDesc": "Enter description...", "editor.addMentions": "Add Mentions", "editor.slateEditorPlaceholder": "Enter description...", "editor.user": "User", "editor.selectUser": "Select User", "editor.searchingFor": "Searching...", "calendar.calendar": "Calendar", "calendar.noEventsFound": "No events found", "calendar.list": "List", "calendar.calendarNotes": "Calendar Notes", "calendar.add": "Add", "calendar.edit": "Edit", "calendar.delete": "Delete", "calendar.addCalendarNote": "Add Calendar Note", "calendar.editCalendarNote": "Edit Calendar Note", "calendar.name": "Name", "calendar.addNote": "Add Note", "calendar.editNote": "Edit Note", "calendar.cancel": "Cancel", "calendar.warning": "Warning", "calendar.deleteModalDesc": "This item will be deleted. Do you confirm?", "calendar.attendingUsers": "Attending Users", "calendar.title": "Title", "calendar.description": "Description", "calendar.startDateTime": "Start Date & Time", "calendar.endDateTime": "End Date & Time", "calendar.isRecurring": "Recurring", "calendar.isAllDay": "All Day", "calendar.assignUser": "Assign User", "calendar.noteType": "Note Type", "calendar.visibilities": "Visibilities", "calendar.recurrenceType": "Recurrence Type", "calendar.reminderChannel": "Reminder Channel", "calendar.relatedCustomer": "Related Customer", "calendar.assignedUser": "Assigned User", "calendar.departments": "Departments", "calendar.attendUsers": "Users to Attend", "calendar.tags": "Tags", "calendar.importanceStatus": "Importance Status", "calendar.isImportant": "Important", "calendar.unimportant": "Unimportant", "calendar.rangeDateTime": "Date Range", "calendar.users": "Users", "calendar.isPublicDesc": "Public Description", "calendar.note": "Note", "calendar.task": "Task", "calendar.meeting": "Meeting", "calendar.callAuto": "Auto Call", "calendar.personal": "Personal", "calendar.departmental": "Departmental", "calendar.public": "Public", "calendar.email": "Email", "calendar.push": "<PERSON><PERSON>", "calendar.sms": "SMS", "calendar.pending": "Pending", "calendar.accepted": "Accepted", "calendar.declined": "Declined", "calendar.daily": "Daily", "calendar.weekly": "Weekly", "calendar.monthly": "Monthly", "calendar.yearly": "Yearly", "resource.resources": "Language Resources", "resource.add": "Add", "resource.edit": "Edit", "resource.delete": "Delete", "resource.name": "Name", "resource.addResource": "Add Language Resource", "resource.editResource": "Edit Language Resource", "resource.cancel": "Cancel", "resource.warning": "Warning", "resource.deleteModalDesc": "This item will be deleted. Do you confirm?", "resource.key": "Key", "resource.value": "Value", "resource.en": "English", "resource.tr": "Turkish", "resource.language": "Language", "resource.enValue": "English Translation", "resource.trValue": "Turkish Translation", "notification.notifications": "Notifications", "notification.readed": "Read", "notification.unReaded": "Unread", "notification.title": "Title", "notification.message": "Message", "notification.user": "User", "notification.date": "Date", "notification.markAllNotificationsRead": "Mark all notifications as read", "notification.markAllMyNotificationRead": "Mark all my notifications as read", "notification.approveChangeAllNotificationStatus": "All notification statuses will change, do you confirm?", "error.unknownError": "An unknown error occurred", "error.notAuthorized": "You are not authorized for this action", "error.errorDuringProcess": "An error occurred during the process", "error.faildProcessError": "Failed to process error message", "error.validationEmailType": "<PERSON><PERSON><PERSON>", "team.team": "Team", "welcome": "Welcome", "filterAlreadyExists": "Filter already exists", "login": "<PERSON><PERSON>", "invalidFilterData": "Invalid filter data", "clearFilterButton": "Clear Filter", "saveQuickFilter": "Save Quick Filter", "deleteQuickFilterModalStatus": "Warning", "deleteQuickFilterModalDesc": "This item will be deleted. Do you confirm?", "deleteQuickFilterModalOK": "OK", "deleteQuickFilterModalCancel": "Cancel", "today": "Today", "previousDay": "Previous Day", "atusageProcessError": "This item is in use by other processes and cannot be deleted", "generalSearch": "Search...", "showAll": "Show All"}